// Mock API service for archive data
interface ArchiveItem {
  id: number;
  title: string;
  type: string;
  createTime: string;
  status: 'pending' | 'to-archive' | 'not-archive' | 'temp-not-archive';
}

interface ArchiveListParams {
  status?: string;
  page?: number;
  pageSize?: number;
  searchQuery?: string;
}

interface ArchiveListResponse {
  data: ArchiveItem[];
  total: number;
  page: number;
  pageSize: number;
}

// Mock data
const mockArchiveData: ArchiveItem[] = [
  {
    id: 1,
    title: '浙江省教育厅关于省政协十二届五次会议第604号提案的答复',
    type: '发文',
    createTime: '2025.06.23 09:48',
    status: 'pending'
  },
  {
    id: 2,
    title: '浙江省教育厅关于我省高校第五轮学科评估结果相关情况的函',
    type: '发文', 
    createTime: '2025.06.23 09:48',
    status: 'to-archive'
  },
  {
    id: 3,
    title: '中共浙江省教育厅党组关于2022年度政治生态建设自评自查情况的报告',
    type: '发文',
    createTime: '2025.06.23 09:48',
    status: 'temp-not-archive'
  },
  {
    id: 4,
    title: '浙江省教育厅 浙江省人力资源和社会保障厅关于进一步深化高校教师职称评价',
    type: '发文',
    createTime: '2025.06.23 09:48',
    status: 'not-archive'
  },
  {
    id: 5,
    title: '浙江省教育厅关于做好2025年普通高校招生工作的通知',
    type: '发文',
    createTime: '2025.06.22 14:30',
    status: 'pending'
  },
  {
    id: 6,
    title: '关于加强高等学校实验室安全管理工作的指导意见',
    type: '发文',
    createTime: '2025.06.22 11:15',
    status: 'to-archive'
  },
  {
    id: 7,
    title: '浙江省教育厅关于开展2025年度教育信息化试点工作的通知',
    type: '发文',
    createTime: '2025.06.21 16:45',
    status: 'temp-not-archive'
  },
  {
    id: 8,
    title: '关于进一步规范中小学教材选用工作的实施意见',
    type: '发文',
    createTime: '2025.06.21 10:20',
    status: 'not-archive'
  }
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

class ArchiveService {
  private data: ArchiveItem[] = [...mockArchiveData];

  async getArchiveList(params: ArchiveListParams = {}): Promise<ArchiveListResponse> {
    // Simulate network delay
    await delay(300);

    const { status = 'all', page = 1, pageSize = 10, searchQuery = '' } = params;

    let filteredData = [...this.data];

    // Filter by status
    if (status !== 'all') {
      filteredData = filteredData.filter(item => item.status === status);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filteredData = filteredData.filter(item => 
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.type.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Calculate pagination
    const total = filteredData.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total,
      page,
      pageSize
    };
  }

  async updateArchiveStatus(ids: number[], status: ArchiveItem['status']): Promise<void> {
    // Simulate network delay
    await delay(200);

    this.data = this.data.map(item => 
      ids.includes(item.id) ? { ...item, status } : item
    );
  }

  async deleteArchives(ids: number[]): Promise<void> {
    // Simulate network delay
    await delay(200);

    this.data = this.data.filter(item => !ids.includes(item.id));
  }

  async addArchive(archiveData: Omit<ArchiveItem, 'id'>): Promise<ArchiveItem> {
    // Simulate network delay
    await delay(300);

    const newArchive: ArchiveItem = {
      ...archiveData,
      id: Math.max(...this.data.map(item => item.id)) + 1
    };

    this.data.push(newArchive);
    return newArchive;
  }
}

export const archiveService = new ArchiveService();
export type { ArchiveItem, ArchiveListParams, ArchiveListResponse };
