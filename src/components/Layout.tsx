import { ReactNode, useState, useRef } from 'react';
import { Search, Power } from 'lucide-react';
import { Input } from './ui/input';

interface LayoutProps {
  children: ReactNode;
  currentUser?: {name: string; organization: string} | null;
  onLogout?: () => void;
  onSearch?: (query: string) => void;
}

export function Layout({ children, currentUser, onLogout, onSearch }: LayoutProps) {
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  const handleSearchMouseEnter = () => {
    setIsSearchExpanded(true);
    setTimeout(() => {
      searchInputRef.current?.focus();
    }, 100);
  };

  const handleSearchMouseLeave = () => {
    if (!searchQuery && !searchInputRef.current?.matches(':focus')) {
      setIsSearchExpanded(false);
    }
  };

  const handleSearchBlur = () => {
    if (!searchQuery) {
      setIsSearchExpanded(false);
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim() && onSearch) {
      onSearch(searchQuery.trim());
    }
  };

  const handleSearchButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSearch) {
      onSearch(searchQuery.trim() || '');
    }
  };

  const handleSearchContainerClick = () => {
    // Only expand the search, don't trigger navigation
    if (!isSearchExpanded) {
      setIsSearchExpanded(true);
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  };
  return (
    <div className="min-h-screen bg-[#ededed]">
      {/* Header */}
      <header className="bg-[#0b6fe9] h-16 flex items-center justify-between px-6">
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
            <div className="w-6 h-6 bg-white/20 rounded"></div>
          </div>
          <h1 className="text-white text-xl font-medium">浙江省教育厅档案管理系统</h1>
        </div>
        
        <div className="flex items-center gap-4">
          <div
            className={`bg-[#107bfd] rounded px-4 py-2 flex items-center gap-2 transition-all duration-300 cursor-pointer ${
              isSearchExpanded ? 'w-[480px]' : 'w-80'
            }`}
            onMouseEnter={handleSearchMouseEnter}
            onMouseLeave={handleSearchMouseLeave}
            onClick={handleSearchContainerClick}
          >
            <Search className="w-4 h-4 text-white flex-shrink-0" />
            {isSearchExpanded ? (
              <form onSubmit={handleSearchSubmit} className="flex-1">
                <Input
                  ref={searchInputRef}
                  type="text"
                  placeholder="输入关键字搜索档案"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onBlur={handleSearchBlur}
                  className="bg-transparent border-none text-white placeholder-white text-sm px-0 h-auto focus:ring-0 focus:outline-none"
                />
              </form>
            ) : (
              <span className="text-white text-sm flex-1">输入关键字搜索档案</span>
            )}
            <div
              className="ml-auto bg-white/10 px-2 py-1 rounded text-xs text-white flex-shrink-0 hover:bg-white/20 transition-colors cursor-pointer"
              onClick={handleSearchButtonClick}
            >
              全文搜索
            </div>
          </div>
          
          <div className="flex items-center gap-6">
            <div className="bg-white/5 rounded px-2 py-1 flex items-center gap-3">
              <div className="w-9 h-9 bg-[#1f5abf] rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-white/20 rounded-full"></div>
              </div>
              <div className="text-white">
                <div className="text-sm">{currentUser?.name || '未知用户'}</div>
                <div className="text-xs opacity-80">{currentUser?.organization || '组织名称'}</div>
              </div>
            </div>
            
            <Power 
              className="w-4 h-4 text-white cursor-pointer hover:text-white/80 transition-colors" 
              onClick={onLogout}
              title="退出登录"
            />
          </div>
        </div>
      </header>

      {children}
    </div>
  );
}