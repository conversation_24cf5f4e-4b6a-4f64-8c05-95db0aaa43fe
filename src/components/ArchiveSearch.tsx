import { useState, useEffect } from 'react';
import { Search, Calendar, Download, Eye, Filter } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';

interface SearchResult {
  id: number;
  title: string;
  type: string;
  category: string;
  createTime: string;
  author: string;
  archiveNumber: string;
  retentionPeriod: string;
  summary: string;
}

const mockResults: SearchResult[] = [
  {
    id: 1,
    title: '浙江省教育厅关于省政协十二届五次会议第604号提案的答复',
    type: '发文',
    category: '文书',
    createTime: '2025.06.23',
    author: '浙江省教育厅',
    archiveNumber: 'ZJS-2025-0001',
    retentionPeriod: '30年',
    summary: '关于高等教育发展规划的相关政策文件，涉及高校建设、教师队伍、学科建设等多个方面的内容...'
  },
  {
    id: 2,
    title: '浙江省教育厅关于我省高校第五轮学科评估结果相关情况的函',
    type: '发文',
    category: '文书',
    createTime: '2025.06.22',
    author: '浙江省教育厅',
    archiveNumber: 'ZJS-2025-0002',
    retentionPeriod: '永久',
    summary: '关于第五轮学科评估的结果通报和相关分析，对各高校学科建设成果进行了总结...'
  },
  {
    id: 3,
    title: '中共浙江省教育厅党组关于2022年度政治生态建设自评自查情况的报告',
    type: '报告',
    category: '党务',
    createTime: '2025.06.21',
    author: '中共浙江省教育厅党组',
    archiveNumber: 'ZJS-2025-0003',
    retentionPeriod: '永久',
    summary: '2022年度政治生态建设的全面自评自查报告，包含组织建设、作风建设、制度建设等内容...'
  }
];

const searchTabs = [
  { id: 'simple', label: '简单检索' },
  { id: 'advanced', label: '高级检索' },
  { id: 'fulltext', label: '全文检索' }
];

// 获取当前年份（当前为2025年）
const getCurrentYear = () => 2025;

// 生成年份列表（倒序排列，从当前年份开始往前20年）
const generateYearsList = (startYear: number = getCurrentYear(), count: number = 20): number[] => {
  return Array.from({ length: count }, (_, i) => startYear - i);
};

const yearsList = generateYearsList();

interface ArchiveSearchProps {
  searchQuery?: string;
}

export function ArchiveSearch({ searchQuery: initialSearchQuery = '' }: ArchiveSearchProps) {
  const [searchType, setSearchType] = useState('simple');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery);
  const [isSearched, setIsSearched] = useState(false);

  // 当从头部搜索传入查询时，自动执行搜索
  useEffect(() => {
    if (initialSearchQuery) {
      setSearchQuery(initialSearchQuery);
      // 自动执行搜索
      const filteredResults = mockResults.filter(item => 
        item.title.includes(initialSearchQuery) || item.summary.includes(initialSearchQuery)
      );
      setResults(filteredResults);
      setIsSearched(true);
    }
  }, [initialSearchQuery]);

  const handleSearch = () => {
    // 模拟搜索
    setResults(mockResults.filter(item => 
      searchQuery ? item.title.includes(searchQuery) || item.summary.includes(searchQuery) : true
    ));
    setIsSearched(true);
  };

  const handleReset = () => {
    setSearchQuery('');
    setResults([]);
    setIsSearched(false);
  };

  const renderSimpleSearch = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">关键词：</span>
          <Input 
            placeholder="请输入关键词" 
            className="flex-1"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">档案类型：</span>
          <Select>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="请选择" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="document">文书</SelectItem>
              <SelectItem value="personnel">人事</SelectItem>
              <SelectItem value="financial">财务</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">年份：</span>
          <Select defaultValue={getCurrentYear().toString()}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="请选择年份" />
            </SelectTrigger>
            <SelectContent>
              {yearsList.map(year => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">保管期限：</span>
          <Select>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="请选择" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10年</SelectItem>
              <SelectItem value="30">30年</SelectItem>
              <SelectItem value="permanent">永久</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );

  const renderAdvancedSearch = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">标题：</span>
          <Input placeholder="请输入标题" className="flex-1" />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">责任者：</span>
          <Input placeholder="请输入责任者" className="flex-1" />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">档号：</span>
          <Input placeholder="请输入档号" className="flex-1" />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">文件编号：</span>
          <Input placeholder="请输入文件编号" className="flex-1" />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">形成时间：</span>
          <div className="flex items-center gap-2 border rounded px-3 py-2 flex-1">
            <Calendar className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-500">开始日期</span>
            <span className="text-gray-400">-</span>
            <span className="text-sm text-gray-500">结束日期</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">主题词：</span>
          <Input placeholder="请输入主题词" className="flex-1" />
        </div>
      </div>
    </div>
  );

  const renderFullTextSearch = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-700 w-20">全文内容：</span>
        <Input 
          placeholder="请输入要搜索的全文内容" 
          className="flex-1"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">档案类型：</span>
          <Select>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="请选择" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="document">文书</SelectItem>
              <SelectItem value="personnel">人事</SelectItem>
              <SelectItem value="financial">财务</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700 w-20">时间范围：</span>
          <div className="flex items-center gap-2 border rounded px-3 py-2 flex-1">
            <Calendar className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-500">开始日期</span>
            <span className="text-gray-400">-</span>
            <span className="text-sm text-gray-500">结束日期</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex-1 bg-gray-100 p-4">
      {/* Search Form */}
      <div className="bg-white rounded p-6 mb-4">
        <Tabs value={searchType} onValueChange={setSearchType}>
          <TabsList className="grid w-full grid-cols-3 max-w-md">
            {searchTabs.map((tab) => (
              <TabsTrigger key={tab.id} value={tab.id}>
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="mt-6">
            <TabsContent value="simple">
              {renderSimpleSearch()}
            </TabsContent>
            <TabsContent value="advanced">
              {renderAdvancedSearch()}
            </TabsContent>
            <TabsContent value="fulltext">
              {renderFullTextSearch()}
            </TabsContent>
          </div>
        </Tabs>

        <div className="flex gap-4 mt-6">
          <Button className="bg-[#2a78ff]" onClick={handleSearch}>
            <Search className="w-4 h-4" />
            检索
          </Button>
          <Button variant="outline" onClick={handleReset}>重置</Button>
        </div>
      </div>

      {/* Search Results */}
      {isSearched && (
        <div className="bg-white rounded">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">检索结果</h3>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>共找到 {results.length} 条记录</span>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4" />
                  导出结果
                </Button>
              </div>
            </div>
          </div>

          <div className="p-4">
            {results.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>没有找到符合条件的档案</p>
                <p className="text-sm mt-2">请尝试修改搜索条件</p>
              </div>
            ) : (
              <div className="space-y-4">
                {results.map((result) => (
                  <Card key={result.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-base mb-2">
                            <a href="#" className="text-[#2a78ff] hover:underline">
                              {result.title}
                            </a>
                          </CardTitle>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span>档号：{result.archiveNumber}</span>
                            <span>类型：{result.type}</span>
                            <span>责任者：{result.author}</span>
                            <span>形成时间：{result.createTime}</span>
                            <Badge variant="outline" className="text-blue-600">
                              {result.retentionPeriod}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-1" />
                            预览
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="w-4 h-4 mr-1" />
                            下载
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {result.summary}
                      </p>
                    </CardContent>
                  </Card>
                ))}

                {/* Pagination */}
                <div className="flex items-center justify-end gap-4 mt-6 text-sm text-gray-600 pt-4 border-t">
                  <span>共 {results.length} 条</span>
                  <div className="flex items-center gap-2">
                    <span>每页</span>
                    <Select defaultValue="10">
                      <SelectTrigger className="w-16 h-7">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                      </SelectContent>
                    </Select>
                    <span>条</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" disabled>
                      上一页
                    </Button>
                    <span className="text-blue-600">1</span>
                    <Button variant="outline" size="sm" disabled>
                      下一页
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>前往</span>
                    <Input className="w-12 h-7" defaultValue="1" />
                    <span>页</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}