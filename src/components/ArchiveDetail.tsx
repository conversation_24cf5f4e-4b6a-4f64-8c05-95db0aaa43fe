import { ArrowLeft, Download, Edit, Trash2, Archive } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

interface ArchiveDetailProps {
  archiveId: string;
  onBack: () => void;
  onEdit?: () => void;
}

// 模拟档案详情数据
const getArchiveDetail = (id: string) => {
  const mockDetails: Record<string, any> = {
    '1': {
      id: '1',
      title: '浙江省教育厅关于省政协十二届五次会议第604号提案的答复',
      archiveNumber: 'ZJS-2025-0001',
      fondNumber: 'ZJS',
      year: '2025',
      retentionPeriod: '30年',
      organization: '办公室',
      serialNumber: '1',
      responsible: '浙江省教育厅',
      documentNumber: '浙教办[2025]1号',
      formationTime: '2025.06.23',
      pages: '5',
      documentType: '发文',
      keywords: '教育,政策,答复',
      remark: '重要政策文件',
      status: 'pending',
      createTime: '2025.06.23 09:48',
      creator: '张三',
      lastModified: '2025.06.23 10:30',
      attachments: [
        { name: '答复文件.pdf', size: '2.3MB' },
        { name: '相关材料.docx', size: '1.1MB' }
      ]
    },
    '2': {
      id: '2',
      title: '浙江省教育厅关于我省高校第五轮学科评估结果相关情况的函',
      archiveNumber: 'ZJS-2025-0002',
      fondNumber: 'ZJS',
      year: '2025',
      retentionPeriod: '永久',
      organization: '办公室',
      serialNumber: '2',
      responsible: '浙江省教育厅',
      documentNumber: '浙教办[2025]2号',
      formationTime: '2025.06.23',
      pages: '8',
      documentType: '发文',
      keywords: '高校,学科评估,情况说明',
      remark: '学科评估相关文件',
      status: 'to-archive',
      createTime: '2025.06.23 09:48',
      creator: '李四',
      lastModified: '2025.06.23 11:15',
      attachments: [
        { name: '评估结果汇总.xlsx', size: '3.5MB' },
        { name: '情况说明.pdf', size: '1.8MB' }
      ]
    }
  };

  return mockDetails[id] || mockDetails['1'];
};

const statusMap = {
  'pending': { label: '待整理', color: 'bg-orange-100 text-orange-600 border-orange-200' },
  'to-archive': { label: '待归档', color: 'bg-green-100 text-green-600 border-green-200' },
  'temp-not-archive': { label: '暂不归档', color: 'bg-gray-100 text-gray-600 border-gray-200' },
  'not-archive': { label: '不归档', color: 'bg-red-100 text-red-600 border-red-200' },
  'archived': { label: '已归档', color: 'bg-blue-100 text-blue-600 border-blue-200' }
};

export function ArchiveDetail({ archiveId, onBack, onEdit }: ArchiveDetailProps) {
  const archive = getArchiveDetail(archiveId);

  return (
    <div className="flex-1 bg-gray-100 p-4">
      <div className="bg-white rounded p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
              返回
            </Button>
            <div>
              <h1 className="text-xl font-medium">{archive.title}</h1>
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                <span>档号：{archive.archiveNumber}</span>
                <span>创建时间：{archive.createTime}</span>
                <Badge className={statusMap[archive.status as keyof typeof statusMap]?.color || statusMap.pending.color}>
                  {statusMap[archive.status as keyof typeof statusMap]?.label || '待整理'}
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="w-4 h-4" />
              下载
            </Button>
            {onEdit && (
              <Button onClick={onEdit}>
                <Edit className="w-4 h-4" />
                编辑
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 基本信息 */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">基本信息</h3>
              <div className="space-y-3">
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">档号</span>
                  <span className="col-span-2">{archive.archiveNumber}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">全宗号</span>
                  <span className="col-span-2">{archive.fondNumber}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">年度</span>
                  <span className="col-span-2">{archive.year}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">保管期限</span>
                  <span className="col-span-2">{archive.retentionPeriod}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">组织机构</span>
                  <span className="col-span-2">{archive.organization}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">序号</span>
                  <span className="col-span-2">{archive.serialNumber}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">正题名</span>
                  <span className="col-span-2">{archive.title}</span>
                </div>
              </div>
            </div>

            {/* 文件信息 */}
            <div>
              <h3 className="text-lg font-medium mb-4">文件信息</h3>
              <div className="space-y-3">
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">责任人</span>
                  <span className="col-span-2">{archive.responsible}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">文件编号</span>
                  <span className="col-span-2">{archive.documentNumber}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">形成时间</span>
                  <span className="col-span-2">{archive.formationTime}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">文件页数</span>
                  <span className="col-span-2">{archive.pages}页</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">文件类型</span>
                  <span className="col-span-2">{archive.documentType}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 扩展信息 */}
          <div className="space-y-6">
            {/* 主题信息 */}
            <div>
              <h3 className="text-lg font-medium mb-4">主题信息</h3>
              <div className="space-y-3">
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">主题词</span>
                  <span className="col-span-2">{archive.keywords}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">备注</span>
                  <span className="col-span-2">{archive.remark}</span>
                </div>
              </div>
            </div>

            {/* 系统信息 */}
            <div>
              <h3 className="text-lg font-medium mb-4">系统信息</h3>
              <div className="space-y-3">
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">创建时间</span>
                  <span className="col-span-2">{archive.createTime}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">创建人</span>
                  <span className="col-span-2">{archive.creator}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">最后修改</span>
                  <span className="col-span-2">{archive.lastModified}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 py-2 border-b border-gray-100">
                  <span className="text-gray-600">状态</span>
                  <span className="col-span-2">
                    <Badge className={statusMap[archive.status as keyof typeof statusMap]?.color || statusMap.pending.color}>
                      {statusMap[archive.status as keyof typeof statusMap]?.label || '待整理'}
                    </Badge>
                  </span>
                </div>
              </div>
            </div>

            {/* 附件信息 */}
            <div>
              <h3 className="text-lg font-medium mb-4">附件信息</h3>
              <div className="space-y-2">
                {archive.attachments.map((attachment: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded">
                    <div className="flex items-center gap-3">
                      <Archive className="w-5 h-5 text-gray-400" />
                      <div>
                        <div className="font-medium">{attachment.name}</div>
                        <div className="text-sm text-gray-500">{attachment.size}</div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}