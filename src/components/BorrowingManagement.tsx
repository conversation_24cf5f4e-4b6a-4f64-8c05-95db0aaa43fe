import { useState, useMemo, useCallback } from 'react';
import { Search, Calendar, Download, Eye, Plus, User, FileText, CheckCircle, Clock, XCircle } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';

interface BorrowingRecord {
  id: number;
  archiveTitle: string;
  archiveNumber: string;
  archiveType: '电子档案' | '实物档案';
  borrower: string;
  borrowerUnit: string;
  borrowTime: string;
  purpose: string;
  status: 'applying' | 'borrowed';
  registrar: string;
  downloadCount: number;
}

interface ApprovalRecord {
  id: number;
  step: string;
  approver: string;
  approverUnit: string;
  approvalTime: string;
  opinion: string;
  status: 'pending' | 'approved' | 'rejected';
}

interface RelatedArchive {
  id: number;
  title: string;
  archiveNumber: string;
  fileType: string;
  fileSize: string;
  uploadTime: string;
}

const mockRecords: BorrowingRecord[] = [
  {
    id: 1,
    archiveTitle: '浙江省教育厅关于省政协十二届五次会议第604号提案的答复',
    archiveNumber: 'ZJS-2025-0001',
    archiveType: '电子档案',
    borrower: '张三',
    borrowerUnit: '高等教育处',
    borrowTime: '2025.06.23 09:48',
    purpose: '工作参考',
    status: 'borrowed',
    registrar: '档案管理员',
    downloadCount: 3
  },
  {
    id: 2,
    archiveTitle: '浙江省教育厅关于我省高校第五轮学科评估结果相关情况的函',
    archiveNumber: 'ZJS-2025-0002',
    archiveType: '电子档案',
    borrower: '李四',
    borrowerUnit: '发展规划处',
    borrowTime: '2025.06.22 14:30',
    purpose: '研究分析',
    status: 'applying',
    registrar: '档案管理员',
    downloadCount: 1
  },
  {
    id: 3,
    archiveTitle: '中共浙江省教育厅党组关于2022年度政治生态建设自评自查情况的报告',
    archiveNumber: 'ZJS-2025-0003',
    archiveType: '电子档案',
    borrower: '王五',
    borrowerUnit: '组织人事处',
    borrowTime: '2025.06.21 16:20',
    purpose: '工作总结',
    status: 'borrowed',
    registrar: '档案管理员',
    downloadCount: 2
  }
];

const mockApprovalRecords: { [key: number]: ApprovalRecord[] } = {
  1: [
    {
      id: 1,
      step: '申请提交',
      approver: '张三',
      approverUnit: '高等教育处',
      approvalTime: '2025.06.22 09:30',
      opinion: '因工作需要申请借阅该档案',
      status: 'approved'
    },
    {
      id: 2,
      step: '处室审核',
      approver: '李主任',
      approverUnit: '高等教育处',
      approvalTime: '2025.06.22 14:20',
      opinion: '同意借阅',
      status: 'approved'
    },
    {
      id: 3,
      step: '档案馆审批',
      approver: '档案管理员',
      approverUnit: '档案馆',
      approvalTime: '2025.06.23 09:48',
      opinion: '审核通过，同意借阅',
      status: 'approved'
    }
  ],
  2: [
    {
      id: 1,
      step: '申请提交',
      approver: '李四',
      approverUnit: '发展规划处',
      approvalTime: '2025.06.22 09:15',
      opinion: '为学科评估分析需要借阅此档案',
      status: 'approved'
    },
    {
      id: 2,
      step: '处室审核',
      approver: '王处长',
      approverUnit: '发展规划处',
      approvalTime: '2025.06.22 11:30',
      opinion: '同意借阅',
      status: 'approved'
    },
    {
      id: 3,
      step: '档案馆审批',
      approver: '档案管理员',
      approverUnit: '档案馆',
      approvalTime: '',
      opinion: '',
      status: 'pending'
    }
  ]
};

const mockRelatedArchives: { [key: number]: RelatedArchive[] } = {
  1: [
    {
      id: 1,
      title: '浙江省教育厅关于省政协十二届五次会议第604号提案的答复.pdf',
      archiveNumber: 'ZJS-2025-0001',
      fileType: 'PDF',
      fileSize: '1.2MB',
      uploadTime: '2025.06.20 15:30'
    },
    {
      id: 2,
      title: '提案答复附件材料.docx',
      archiveNumber: 'ZJS-2025-0001-01',
      fileType: 'DOCX',
      fileSize: '856KB',
      uploadTime: '2025.06.20 15:32'
    }
  ],
  2: [
    {
      id: 1,
      title: '浙江省教育厅关于我省高校第五轮学科评估结果相关情况的函.pdf',
      archiveNumber: 'ZJS-2025-0002',
      fileType: 'PDF',
      fileSize: '2.1MB',
      uploadTime: '2025.06.19 10:20'
    }
  ],
  3: [
    {
      id: 1,
      title: '中共浙江省教育厅党组关于2022年度政治生态建设自评自查情况的报告.pdf',
      archiveNumber: 'ZJS-2025-0003',
      fileType: 'PDF',
      fileSize: '3.5MB',
      uploadTime: '2025.06.18 14:15'
    },
    {
      id: 2,
      title: '政治生态建设自评自查附表.xlsx',
      archiveNumber: 'ZJS-2025-0003-01',
      fileType: 'XLSX',
      fileSize: '125KB',
      uploadTime: '2025.06.18 14:18'
    }
  ]
};

const borrowingTabs = [
  { id: 'records', label: '借阅记录' },
  { id: 'statistics', label: '借阅统计' }
];

interface AddRecordDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

interface BorrowingDetailDialogProps {
  open: boolean;
  onClose: () => void;
  record: BorrowingRecord | null;
}

function BorrowingDetailDialog({ open, onClose, record }: BorrowingDetailDialogProps) {
  if (!open || !record) return null;

  const approvalRecords = mockApprovalRecords[record.id] || [];
  const relatedArchives = mockRelatedArchives[record.id] || [];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return '已通过';
      case 'pending':
        return '待审批';
      case 'rejected':
        return '已拒绝';
      default:
        return '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>借阅记录详情</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="bg-gray-50 p-4 rounded">
            <h3 className="font-medium mb-3">基本信息</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">档案标题：</span>
                <span className="text-gray-900">{record.archiveTitle}</span>
              </div>
              <div>
                <span className="text-gray-600">档号：</span>
                <span className="text-gray-900">{record.archiveNumber}</span>
              </div>
              <div>
                <span className="text-gray-600">档案类型：</span>
                <Badge className="bg-blue-100 text-blue-600 border-blue-200">
                  {record.archiveType}
                </Badge>
              </div>
              <div>
                <span className="text-gray-600">借阅状态：</span>
                <Badge className={record.status === 'borrowed' ? 'bg-green-100 text-green-600 border-green-200' : 'bg-yellow-100 text-yellow-600 border-yellow-200'}>
                  {record.status === 'borrowed' ? '已借阅' : '申请中'}
                </Badge>
              </div>
              <div>
                <span className="text-gray-600">借阅人：</span>
                <span className="text-gray-900">{record.borrower}</span>
              </div>
              <div>
                <span className="text-gray-600">借阅单位：</span>
                <span className="text-gray-900">{record.borrowerUnit}</span>
              </div>
              <div>
                <span className="text-gray-600">借阅时间：</span>
                <span className="text-gray-900">{record.borrowTime}</span>
              </div>
              <div>
                <span className="text-gray-600">借阅目的：</span>
                <span className="text-gray-900">{record.purpose}</span>
              </div>
            </div>
          </div>

          {/* 审批流程 */}
          <div className="space-y-3">
            <h3 className="font-medium">审批流程</h3>
            <div className="space-y-3">
              {approvalRecords.map((approval, index) => (
                <div key={approval.id} className="flex items-start gap-4 p-3 border rounded">
                  <div className="flex flex-col items-center">
                    {getStatusIcon(approval.status)}
                    {index < approvalRecords.length - 1 && (
                      <div className="w-0.5 h-8 bg-gray-300 mt-2"></div>
                    )}
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{approval.step}</span>
                      <Badge variant="outline" className={
                        approval.status === 'approved' ? 'text-green-600 border-green-200' :
                        approval.status === 'pending' ? 'text-yellow-600 border-yellow-200' :
                        'text-red-600 border-red-200'
                      }>
                        {getStatusText(approval.status)}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      审批人：{approval.approver} ({approval.approverUnit})
                    </div>
                    {approval.approvalTime && (
                      <div className="text-sm text-gray-600">
                        审批时间：{approval.approvalTime}
                      </div>
                    )}
                    {approval.opinion && (
                      <div className="text-sm text-gray-700">
                        审批意见：{approval.opinion}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 关联电子档案 */}
          <div className="space-y-3">
            <h3 className="font-medium">关联电子档案</h3>
            <div className="space-y-2">
              {relatedArchives.map((archive) => (
                <div key={archive.id} className="flex items-center justify-between p-3 border rounded hover:bg-gray-50">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-blue-500" />
                    <div>
                      <div className="font-medium text-sm">{archive.title}</div>
                      <div className="text-xs text-gray-500">
                        档号：{archive.archiveNumber} | 大小：{archive.fileSize} | 上传时间：{archive.uploadTime}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="link" size="sm" className="text-[#2a78ff] p-0">
                      <Eye className="w-4 h-4 mr-1" />
                      预览
                    </Button>
                    <Button variant="link" size="sm" className="text-[#2a78ff] p-0">
                      <Download className="w-4 h-4 mr-1" />
                      下载
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>关闭</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function AddRecordDialog({ open, onClose, onSubmit }: AddRecordDialogProps) {
  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>人工登记借阅记录</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-20 text-right">档案标题：</label>
              <Input placeholder="请选择或输入档案标题" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-20 text-right">档号：</label>
              <Input placeholder="自动填充或手动输入" className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-20 text-right">档案类型：</label>
              <Select>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="请选择档案类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="electronic">电子档案</SelectItem>
                  <SelectItem value="physical">实物档案</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-20 text-right">借阅人：</label>
              <Input placeholder="请输入借阅人姓名" className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-20 text-right">借阅单位：</label>
              <Input placeholder="请输入借阅单位" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-20 text-right"></label>
              <div className="flex-1"></div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-20 text-right">借阅时间：</label>
              <div className="flex items-center gap-2 border rounded px-3 py-2 flex-1">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-500">选择日期时间</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-20 text-right">借阅目的：</label>
              <Select>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="请选择" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="work">工作参考</SelectItem>
                  <SelectItem value="research">研究分析</SelectItem>
                  <SelectItem value="summary">工作总结</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-20 text-right">备注：</label>
            <Input placeholder="请输入备注信息（选填）" className="flex-1" />
          </div>
        </div>

        <div className="flex justify-end gap-4">
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button className="bg-[#2a78ff]" onClick={() => onSubmit({})}>确定</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export function BorrowingManagement() {
  const [activeTab, setActiveTab] = useState('records');
  const [records, setRecords] = useState(mockRecords);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<BorrowingRecord | null>(null);

  const activeRecords = useMemo(() => 
    records.filter(record => record.status === 'borrowed'), 
    [records]
  );
  
  const totalDownloads = useMemo(() => 
    records.reduce((sum, record) => sum + record.downloadCount, 0), 
    [records]
  );

  const handleAddDialogClose = useCallback(() => {
    setShowAddDialog(false);
  }, []);

  const handleAddDialogSubmit = useCallback((data: any) => {
    console.log('Add borrowing record:', data);
    setShowAddDialog(false);
  }, []);

  const handleDetailDialogClose = useCallback(() => {
    setShowDetailDialog(false);
    setSelectedRecord(null);
  }, []);

  const handleViewDetail = useCallback((record: BorrowingRecord) => {
    setSelectedRecord(record);
    setShowDetailDialog(true);
  }, []);

  const renderRecordsTab = () => (
    <div className="space-y-4">
      {/* Search Form */}
      <div className="bg-white rounded p-4 border">
        <div className="flex items-center gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">借阅人：</span>
            <Input placeholder="请输入借阅人姓名" className="w-40" />
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">借阅时间：</span>
            <div className="flex items-center gap-2 border rounded px-3 py-2 w-56">
              <Calendar className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-700">开始日期</span>
              <span className="text-gray-400">-</span>
              <span className="text-sm text-gray-700">结束日期</span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">档案标题：</span>
            <Input placeholder="请输入档案标题" className="w-40" />
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">档案类型：</span>
            <Select>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="全部" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="electronic">电子档案</SelectItem>
                <SelectItem value="physical">实物档案</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">状态：</span>
            <Select>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="全部" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="applying">申请中</SelectItem>
                <SelectItem value="borrowed">已借阅</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button className="bg-[#2a78ff] flex items-center gap-1.5">
            <Search className="w-4 h-4" />
            查询
          </Button>
          <Button variant="outline">重置</Button>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <Button
          className="bg-[#2a78ff] flex items-center gap-1.5"
          onClick={() => setShowAddDialog(true)}
        >
          <Plus className="w-4 h-4" />
          人工登记借阅记录
        </Button>
        <Button variant="outline" className="flex items-center gap-1.5">
          <Download className="w-4 h-4" />
          导出记录
        </Button>
      </div>

      {/* Records Table */}
      <div className="bg-white rounded border overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 flex items-center text-sm font-medium text-gray-600 border-b">
          <div className="w-16 p-4 border-r">编号</div>
          <div className="flex-1 p-4 border-r">档案标题</div>
          <div className="w-32 p-4 border-r">档号</div>
          <div className="w-24 p-4 border-r">档案类型</div>
          <div className="w-24 p-4 border-r">借阅人</div>
          <div className="w-32 p-4 border-r">借阅单位</div>
          <div className="w-40 p-4 border-r">借阅时间</div>
          <div className="w-24 p-4 border-r">下载次数</div>
          <div className="w-20 p-4 border-r">状态</div>
          <div className="w-32 p-4">操作</div>
        </div>

        {/* Rows */}
        {records.map((record, index) => (
          <div key={record.id} className={`flex items-center text-sm ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} border-b last:border-b-0`}>
            <div className="w-16 p-4 border-r text-gray-900">{record.id}</div>
            <div className="flex-1 p-4 border-r text-gray-900 truncate">{record.archiveTitle}</div>
            <div className="w-32 p-4 border-r text-gray-900">{record.archiveNumber}</div>
            <div className="w-24 p-4 border-r text-gray-900">
              <Badge className="bg-blue-100 text-blue-600 border-blue-200">
                {record.archiveType}
              </Badge>
            </div>
            <div className="w-24 p-4 border-r text-gray-900">{record.borrower}</div>
            <div className="w-32 p-4 border-r text-gray-900">{record.borrowerUnit}</div>
            <div className="w-40 p-4 border-r text-gray-900">{record.borrowTime}</div>
            <div className="w-24 p-4 border-r text-gray-900 text-center">{record.downloadCount}</div>
            <div className="w-20 p-4 border-r">
              <Badge className={record.status === 'borrowed' ? 'bg-green-100 text-green-600 border-green-200' : 'bg-yellow-100 text-yellow-600 border-yellow-200'}>
                {record.status === 'borrowed' ? '已借阅' : '申请中'}
              </Badge>
            </div>
            <div className="w-32 p-4">
              <Button 
                variant="link" 
                size="sm" 
                className="text-[#2a78ff] p-0"
                onClick={() => handleViewDetail(record)}
              >
                <Eye className="w-4 h-4 mr-1" />
                查看详情
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-end gap-4 text-sm text-gray-600">
        <span>共 {records.length} 条</span>
        <div className="flex items-center gap-2">
          <span>每页</span>
          <Select defaultValue="10">
            <SelectTrigger className="w-16 h-7">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
          <span>条</span>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <span className="text-blue-600">1</span>
          <Button variant="outline" size="sm" disabled>
            下一页
          </Button>
        </div>
      </div>
    </div>
  );

  const renderStatisticsTab = () => (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">总借阅记录</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <User className="w-5 h-5 text-blue-500" />
              <span className="text-2xl font-bold text-blue-600">{records.length}</span>
              <span className="text-sm text-gray-500">条</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">已借阅记录</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <User className="w-5 h-5 text-green-500" />
              <span className="text-2xl font-bold text-green-600">{activeRecords.length}</span>
              <span className="text-sm text-gray-500">条</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">总下载次数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Download className="w-5 h-5 text-purple-500" />
              <span className="text-2xl font-bold text-purple-600">{totalDownloads}</span>
              <span className="text-sm text-gray-500">次</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">平均下载次数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Download className="w-5 h-5 text-orange-500" />
              <span className="text-2xl font-bold text-orange-600">{(totalDownloads / records.length).toFixed(1)}</span>
              <span className="text-sm text-gray-500">次/条</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Hot Archives */}
      <Card>
        <CardHeader>
          <CardTitle>热门档案排行</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {records
              .sort((a, b) => b.downloadCount - a.downloadCount)
              .slice(0, 5)
              .map((record, index) => (
                <div key={record.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      index === 2 ? 'bg-orange-400' : 'bg-gray-300'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{record.archiveTitle}</div>
                      <div className="text-xs text-gray-500">档号：{record.archiveNumber}</div>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-blue-600">
                    {record.downloadCount} 次
                  </Badge>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Borrowers */}
      <Card>
        <CardHeader>
          <CardTitle>最近借阅人员</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {records
              .sort((a, b) => new Date(b.borrowTime).getTime() - new Date(a.borrowTime).getTime())
              .slice(0, 5)
              .map((record) => (
                <div key={record.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                      {record.borrower.charAt(0)}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{record.borrower}</div>
                      <div className="text-xs text-gray-500">{record.borrowerUnit}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-900">{record.borrowTime}</div>
                    <div className="text-xs text-gray-500">{record.purpose}</div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="flex-1 bg-gray-100 p-4">
      <div className="bg-white rounded">
        <div className="p-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 max-w-md">
              {borrowingTabs.map((tab) => (
                <TabsTrigger key={tab.id} value={tab.id}>
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>

            <div className="mt-6">
              <TabsContent value="records">
                {renderRecordsTab()}
              </TabsContent>
              <TabsContent value="statistics">
                {renderStatisticsTab()}
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>

      <AddRecordDialog 
        open={showAddDialog}
        onClose={handleAddDialogClose}
        onSubmit={handleAddDialogSubmit}
      />

      <BorrowingDetailDialog
        open={showDetailDialog}
        onClose={handleDetailDialogClose}
        record={selectedRecord}
      />
    </div>
  );
}