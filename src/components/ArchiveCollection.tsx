import { useState } from 'react';
import { Search, Calendar, ChevronDown, Plus, X, Archive, Tag, Clock, RotateCcw, Loader2 } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Badge } from './ui/badge';
import { Checkbox } from './ui/checkbox';
import { useArchiveData } from '../hooks/useArchiveData';
import { ArchiveItem } from '../services/archiveService';

// ArchiveItem interface is now imported from the service

const statusMap = {
  'pending': { label: '待整理', color: 'bg-orange-100 text-orange-600 border-orange-200' },
  'to-archive': { label: '待归档', color: 'bg-green-100 text-green-600 border-green-200' },
  'temp-not-archive': { label: '暂不归档', color: 'bg-gray-100 text-gray-600 border-gray-200' },
  'not-archive': { label: '不归档', color: 'bg-red-100 text-red-600 border-red-200' }
};

const filterTabs = [
  { id: 'all', label: '全部' },
  { id: 'pending', label: '待整理' },
  { id: 'to-archive', label: '待归档' },
  { id: 'not-archive', label: '不归档' },
  { id: 'temp-not-archive', label: '暂不归档' }
];

// 获取当前年份（当前为2025年）
const getCurrentYear = () => 2025;

// 生成年份列表（倒序排列，从当前年份开始往前10年）
const generateYearsList = (startYear: number = getCurrentYear(), count: number = 10): number[] => {
  return Array.from({ length: count }, (_, i) => startYear - i);
};

const yearsList = generateYearsList();

interface AddDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

interface ArchiveDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  archiveData?: ArchiveItem;
}

function AddDialog({ open, onClose, onSubmit }: AddDialogProps) {
  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[856px] max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-medium">新增</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">档号：</label>
              <Input defaultValue="ZJS-2025-0001" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">全宗号：</label>
              <Input placeholder="请输入" className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">年度：</label>
              <Select defaultValue={getCurrentYear().toString()}>
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {yearsList.map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">保管期限：</label>
              <Select>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="10年/30年/永久" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10年</SelectItem>
                  <SelectItem value="30">30年</SelectItem>
                  <SelectItem value="permanent">永久</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">组织机构：</label>
              <Select>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="请选择" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="office">办公室</SelectItem>
                  <SelectItem value="finance">财务处</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">序号：</label>
              <div className="flex items-center border rounded flex-1">
                <Button variant="ghost" size="sm" className="px-2">-</Button>
                <span className="flex-1 text-center">0</span>
                <Button variant="ghost" size="sm" className="px-2">+</Button>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">正题名：</label>
            <Input placeholder="请输入" className="flex-1" />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">责任人：</label>
              <Input placeholder="请输入" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">文件编号：</label>
              <Input placeholder="请输入" className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">形成时间：</label>
              <Input placeholder="请输入" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">文件页数：</label>
              <Input placeholder="请输入" className="flex-1" />
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">附件：</label>
            <Button className="bg-[#2a78ff] text-white flex items-center gap-1.5">
              <Plus className="w-4 h-4" />
              上传附件
            </Button>
          </div>
        </div>

        <div className="flex justify-end gap-4 p-6 border-t">
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button className="bg-[#2a78ff]" onClick={() => onSubmit({})}>确定</Button>
        </div>
      </div>
    </div>
  );
}

// 归档整理弹窗 - 与手工录入相同的界面，但填充当前档案数据
function ArchiveDialog({ open, onClose, onSubmit, archiveData }: ArchiveDialogProps) {
  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[856px] max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-medium">归档整理</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">档号：</label>
              <Input defaultValue={`ZJS-${getCurrentYear()}-${String(archiveData?.id || 1).padStart(4, '0')}`} className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">全宗号：</label>
              <Input defaultValue="ZJS" className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">年度：</label>
              <Select defaultValue={getCurrentYear().toString()}>
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {yearsList.map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">保管期限：</label>
              <Select defaultValue="30">
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10年</SelectItem>
                  <SelectItem value="30">30年</SelectItem>
                  <SelectItem value="permanent">永久</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">组织机构：</label>
              <Select defaultValue="office">
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="office">办公室</SelectItem>
                  <SelectItem value="finance">财务处</SelectItem>
                  <SelectItem value="personnel">人事处</SelectItem>
                  <SelectItem value="academic">学术处</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">序号：</label>
              <div className="flex items-center border rounded flex-1">
                <Button variant="ghost" size="sm" className="px-2">-</Button>
                <span className="flex-1 text-center">{archiveData?.id || 1}</span>
                <Button variant="ghost" size="sm" className="px-2">+</Button>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">正题名：</label>
            <Input defaultValue={archiveData?.title || ''} className="flex-1" />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">责任人：</label>
              <Input defaultValue="浙江省教育厅" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">文件编号：</label>
              <Input defaultValue={`浙教办[${getCurrentYear()}]${archiveData?.id || 1}号`} className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">形成时间：</label>
              <Input defaultValue={archiveData?.createTime?.split(' ')[0] || ''} className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">文件页数：</label>
              <Input defaultValue="5" className="flex-1" />
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">主题词：</label>
            <Input defaultValue="教育,政策,答复" className="flex-1" />
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">备注：</label>
            <Input placeholder="请输入备注信息" className="flex-1" />
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">附件：</label>
            <Button className="bg-[#2a78ff] text-white flex items-center gap-1.5">
              <Plus className="w-4 h-4" />
              上传附件
            </Button>
          </div>
        </div>

        <div className="flex justify-end gap-4 p-6 border-t">
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button className="bg-[#2a78ff]" onClick={() => onSubmit({})}>保存并归档</Button>
        </div>
      </div>
    </div>
  );
}

interface ArchiveCollectionProps {
  onViewArchive?: (archiveId: string) => void;
}

export function ArchiveCollection({ onViewArchive }: ArchiveCollectionProps) {
  const {
    data,
    loading,
    error,
    total,
    page,
    pageSize,
    status,
    searchQuery,
    setStatus,
    setSearchQuery,
    setPage,
    refetch,
    updateArchiveStatus,
    deleteArchives,
    addArchive
  } = useArchiveData({ initialStatus: 'all', pageSize: 10 });

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [currentArchiveItem, setCurrentArchiveItem] = useState<ArchiveItem | undefined>();

  const handleSelectAll = (checked: boolean) => {
    setSelectedItems(checked ? data.map(item => item.id) : []);
  };

  const handleSelectItem = (id: number, checked: boolean) => {
    setSelectedItems(prev =>
      checked ? [...prev, id] : prev.filter(itemId => itemId !== id)
    );
  };

  const handleTabChange = (tabId: string) => {
    setStatus(tabId);
    setSelectedItems([]); // Clear selection when changing tabs
  };

  // 归档整理 - 打开归档整理弹窗
  const handleArchiveManagement = () => {
    if (selectedItems.length === 1) {
      const selectedItem = data.find(item => item.id === selectedItems[0]);
      setCurrentArchiveItem(selectedItem);
      setShowArchiveDialog(true);
    }
  };

  // 标记为不归档
  const handleMarkAsNotArchive = async () => {
    await updateArchiveStatus(selectedItems, 'not-archive');
    setSelectedItems([]);
  };

  // 标记为暂不归档
  const handleMarkAsTempNotArchive = async () => {
    await updateArchiveStatus(selectedItems, 'temp-not-archive');
    setSelectedItems([]);
  };

  // 取消标记
  const handleCancelMark = async () => {
    await updateArchiveStatus(selectedItems, 'pending');
    setSelectedItems([]);
  };

  // 归档整理完成
  const handleArchiveSubmit = (archiveData: any) => {
    if (currentArchiveItem) {
      setData(prev => 
        prev.map(item => 
          item.id === currentArchiveItem.id ? { ...item, status: 'to-archive' } : item
        )
      );
    }
    setShowArchiveDialog(false);
    setCurrentArchiveItem(undefined);
    setSelectedItems([]);
  };

  return (
    <div className="flex-1 bg-gray-100 p-4">
      {/* Search Form */}
      <div className="bg-white rounded p-4 mb-4">
        <div className="flex items-center gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">年份：</span>
            <Select defaultValue={getCurrentYear().toString()}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {yearsList.map(year => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">创建日期：</span>
            <div className="flex items-center gap-2 border rounded px-3 py-2 w-56">
              <Calendar className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-700">开始日期</span>
              <span className="text-gray-400">-</span>
              <span className="text-sm text-gray-700">结束日期</span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">标题：</span>
            <Input placeholder="请输入" className="w-40" />
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">公文类型：</span>
            <Select>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="请选择" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="official">公文</SelectItem>
                <SelectItem value="notice">通知</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button className="bg-[#2a78ff]">查询</Button>
          <Button variant="outline">重置</Button>
        </div>
      </div>

      <div className="flex gap-4 h-[calc(100vh-240px)]">
        {/* Filter Sidebar */}
        <div className="w-32 bg-white rounded overflow-hidden">
          {filterTabs.map((tab) => (
            <div
              key={tab.id}
              className={`h-10 flex items-center justify-center cursor-pointer border-l-4 ${
                status === tab.id
                  ? 'bg-[#d9e9ff] border-l-[#2a78ff] text-[#2a78ff]'
                  : 'bg-white border-l-transparent text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => handleTabChange(tab.id)}
            >
              <span className="text-sm">{tab.label}</span>
            </div>
          ))}
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white rounded border-l">
          <div className="p-4">
            {/* Action Buttons */}
            <div className="flex gap-4 mb-4">
              <Button
                className={`${selectedItems.length === 1 ? 'bg-[#2a78ff]' : 'bg-[#92b9fd]'} text-white flex items-center gap-1.5`}
                disabled={selectedItems.length !== 1}
                onClick={handleArchiveManagement}
              >
                <Archive className="w-4 h-4" />
                归档整理
              </Button>
              <Button
                className={`${selectedItems.length > 0 ? 'bg-[#2a78ff]' : 'bg-[#92b9fd]'} text-white flex items-center gap-1.5`}
                disabled={selectedItems.length === 0}
                onClick={handleMarkAsNotArchive}
              >
                <Tag className="w-4 h-4" />
                标记为不归档
              </Button>
              <Button
                className={`${selectedItems.length > 0 ? 'bg-[#2a78ff]' : 'bg-[#92b9fd]'} text-white flex items-center gap-1.5`}
                disabled={selectedItems.length === 0}
                onClick={handleMarkAsTempNotArchive}
              >
                <Clock className="w-4 h-4" />
                标记为暂不归档
              </Button>
              <Button
                className={`${selectedItems.length > 0 ? 'bg-[#2a78ff]' : 'bg-[#92b9fd]'} text-white flex items-center gap-1.5`}
                disabled={selectedItems.length === 0}
                onClick={handleCancelMark}
              >
                <RotateCcw className="w-4 h-4" />
                取消标记
              </Button>
            </div>

            {/* Table */}
            <div className="border rounded overflow-hidden">
              {/* Header */}
              <div className="bg-gray-50 flex items-center text-sm font-medium text-gray-600 border-b">
                <div className="w-16 p-4 border-r">
                  <Checkbox
                    checked={selectedItems.length === data.length && data.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </div>
                <div className="w-16 p-4 border-r">编号</div>
                <div className="flex-1 p-4 border-r">标题</div>
                <div className="w-32 p-4 border-r">公文类型</div>
                <div className="w-48 p-4 border-r">创建时间</div>
                <div className="w-24 p-4">状态</div>
              </div>

              {/* Loading State */}
              {loading && (
                <div className="flex items-center justify-center p-8 gap-2">
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <span className="text-gray-500">加载中...</span>
                </div>
              )}

              {/* Error State */}
              {error && (
                <div className="flex items-center justify-center p-8">
                  <span className="text-red-500">加载失败: {error}</span>
                </div>
              )}

              {/* Rows */}
              {!loading && !error && data.map((item, index) => {
                // Calculate the actual row number based on pagination
                const rowNumber = (page - 1) * pageSize + index + 1;
                return (
                <div key={item.id} className={`flex items-center text-sm ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} border-b last:border-b-0`}>
                  <div className="w-16 p-4 border-r">
                    <Checkbox
                      checked={selectedItems.includes(item.id)}
                      onCheckedChange={(checked) => handleSelectItem(item.id, checked as boolean)}
                    />
                  </div>
                  <div className="w-16 p-4 border-r text-gray-900">{rowNumber}</div>
                  <div className="flex-1 p-4 border-r text-gray-900 truncate">
                    <button 
                      className="text-blue-600 hover:text-blue-800 hover:underline text-left"
                      onClick={() => onViewArchive?.(item.id.toString())}
                    >
                      {item.title}
                    </button>
                  </div>
                  <div className="w-32 p-4 border-r text-gray-900">{item.type}</div>
                  <div className="w-48 p-4 border-r text-gray-900">{item.createTime}</div>
                  <div className="w-24 p-4">
                    <Badge className={statusMap[item.status].color}>
                      {statusMap[item.status].label}
                    </Badge>
                  </div>
                </div>
                );
              })}
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-end gap-4 mt-4 text-sm text-gray-600">
              <span>共 {total} 条</span>
              <div className="flex items-center gap-2">
                <span>每页</span>
                <Select defaultValue="10">
                  <SelectTrigger className="w-16 h-7">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span>条</span>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={page <= 1}
                  onClick={() => setPage(page - 1)}
                >
                  上一页
                </Button>
                <span className="text-blue-600">{page}</span>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={page >= Math.ceil(total / pageSize)}
                  onClick={() => setPage(page + 1)}
                >
                  下一页
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <span>前往</span>
                <Input className="w-12 h-7" defaultValue="1" />
                <span>页</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AddDialog 
        open={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onSubmit={(data) => {
          console.log('Add record:', data);
          setShowAddDialog(false);
        }}
      />

      <ArchiveDialog 
        open={showArchiveDialog}
        onClose={() => {
          setShowArchiveDialog(false);
          setCurrentArchiveItem(undefined);
        }}
        onSubmit={handleArchiveSubmit}
        archiveData={currentArchiveItem}
      />
    </div>
  );
}