import { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { TrendingUp, FileText, FolderOpen, Download, Users, Calendar } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Button } from './ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from './ui/tabs';

// 模拟数据
const monthlyArchiveData = [
  { month: '1月', count: 120, archived: 100, pending: 20 },
  { month: '2月', count: 150, archived: 130, pending: 20 },
  { month: '3月', count: 180, archived: 160, pending: 20 },
  { month: '4月', count: 140, archived: 120, pending: 20 },
  { month: '5月', count: 200, archived: 180, pending: 20 },
  { month: '6月', count: 190, archived: 170, pending: 20 }
];

const categoryData = [
  { name: '文书档案', value: 450, color: '#2a78ff' },
  { name: '人事档案', value: 280, color: '#52c41a' },
  { name: '财务档案', value: 180, color: '#faad14' },
  { name: '基建档案', value: 120, color: '#f5222d' },
  { name: '照片档案', value: 80, color: '#722ed1' },
  { name: '其他档案', value: 90, color: '#13c2c2' }
];

const borrowingTrendData = [
  { month: '1月', borrowing: 45, downloads: 120 },
  { month: '2月', borrowing: 52, downloads: 140 },
  { month: '3月', borrowing: 48, downloads: 160 },
  { month: '4月', borrowing: 61, downloads: 180 },
  { month: '5月', borrowing: 55, downloads: 200 },
  { month: '6月', borrowing: 67, downloads: 230 }
];

const departmentUsageData = [
  { department: '高等教育处', archives: 85, borrowing: 45 },
  { department: '发展规划处', archives: 72, borrowing: 38 },
  { department: '组织人事处', archives: 68, borrowing: 42 },
  { department: '财务处', archives: 56, borrowing: 28 },
  { department: '基建处', archives: 43, borrowing: 22 },
  { department: '办公室', archives: 39, borrowing: 25 }
];

const retentionPeriodData = [
  { period: '永久', count: 580, percentage: 48.3 },
  { period: '30年', count: 420, percentage: 35.0 },
  { period: '10年', count: 200, percentage: 16.7 }
];

// 获取当前年份（当前为2025年）
const getCurrentYear = () => 2025;

// 生成年份列表（倒序排列，从当前年份开始往前10年）
const generateYearsList = (startYear: number = getCurrentYear(), count: number = 10): number[] => {
  return Array.from({ length: count }, (_, i) => startYear - i);
};

const yearsList = generateYearsList();

export function DataAnalysis() {
  const [timeRange, setTimeRange] = useState(getCurrentYear().toString());

  const totalArchives = categoryData.reduce((sum, item) => sum + item.value, 0);
  const totalBorrowing = borrowingTrendData.reduce((sum, item) => sum + item.borrowing, 0);
  const totalDownloads = borrowingTrendData.reduce((sum, item) => sum + item.downloads, 0);
  const currentMonthArchives = monthlyArchiveData[monthlyArchiveData.length - 1].count;

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">档案总数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalArchives.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +{currentMonthArchives} 本月新增
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">借阅次数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalBorrowing}</div>
            <p className="text-xs text-muted-foreground">
              +12% 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">下载次数</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDownloads}</div>
            <p className="text-xs text-muted-foreground">
              +8% 较上月
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">归档率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">94.5%</div>
            <p className="text-xs text-muted-foreground">
              +2.1% 较上月
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>月度档案数量统计</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyArchiveData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="archived" fill="#2a78ff" name="已归档" />
                <Bar dataKey="pending" fill="#faad14" name="待处理" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>档案分类分布</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={categoryData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name} ${((percentage || 0) * 100).toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>借阅趋势分析</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={borrowingTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="borrowing" stroke="#2a78ff" name="借阅次数" />
              <Line type="monotone" dataKey="downloads" stroke="#52c41a" name="下载次数" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderUsageTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>各处室使用情况</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={departmentUsageData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="department" type="category" width={100} />
              <Tooltip />
              <Bar dataKey="archives" fill="#2a78ff" name="档案数量" />
              <Bar dataKey="borrowing" fill="#52c41a" name="借阅次数" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>保管期限分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {retentionPeriodData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-4 h-4 rounded ${
                      index === 0 ? 'bg-blue-500' : 
                      index === 1 ? 'bg-green-500' : 'bg-yellow-500'
                    }`}></div>
                    <span className="text-sm font-medium">{item.period}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold">{item.count}</div>
                    <div className="text-xs text-gray-500">{item.percentage}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>热门档案排行</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { title: '浙江省教育厅关于省政协十二届五次会议第604号提案的答复', count: 28 },
                { title: '浙江省教育厅关于我省高校第五轮学科评估结果相关情况的函', count: 24 },
                { title: '中共浙江省教育厅党组关于2022年度政治生态建设自评自查情况的报告', count: 19 },
                { title: '浙江省教育厅关于进一步深化高校教师职称评价改革的通知', count: 16 },
                { title: '浙江省教育厅关于做好2025年高校招生工作的通知', count: 12 }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      index === 2 ? 'bg-orange-400' : 'bg-gray-300'
                    }`}>
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium truncate max-w-[300px]">{item.title}</div>
                    </div>
                  </div>
                  <div className="text-sm font-bold text-blue-600">{item.count}次</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="flex-1 bg-gray-100 p-4">
      <div className="bg-white rounded">
        <div className="p-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">数据分析</h2>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">时间范围：</span>
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {yearsList.map(year => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}年
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button variant="outline">
                <Download className="w-4 h-4" />
                导出报告
              </Button>
            </div>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-2 max-w-md">
              <TabsTrigger value="overview">数据概览</TabsTrigger>
              <TabsTrigger value="usage">使用分析</TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <TabsContent value="overview">
                {renderOverviewTab()}
              </TabsContent>
              <TabsContent value="usage">
                {renderUsageTab()}
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}