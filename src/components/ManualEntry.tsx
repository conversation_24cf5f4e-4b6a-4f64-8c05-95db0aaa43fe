import { useState } from 'react';
import { Search, Calendar, ChevronDown, Plus, Trash2, Edit, X, Upload, Download } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Badge } from './ui/badge';
import { Checkbox } from './ui/checkbox';

// 档案分类树形结构
interface ArchiveCategory {
  id: string;
  label: string;
  children?: ArchiveCategory[];
  expanded?: boolean;
}

// 获取当前年份（当前为2025年）
const getCurrentYear = () => 2025;

// 生成年份列表（倒序排列，从当前年份开始往前10年）
const generateYearsList = (startYear: number = getCurrentYear(), count: number = 10): number[] => {
  return Array.from({ length: count }, (_, i) => startYear - i);
};

// 生成档案分类的年份子节点
const generateYearCategories = (categoryPrefix: string, years: number[] = generateYearsList()) => {
  return years.map(year => ({
    id: `${categoryPrefix}-${year}`,
    label: year.toString(),
    children: [
      { id: `${categoryPrefix}-${year}-10year`, label: '10年' },
      { id: `${categoryPrefix}-${year}-30year`, label: '30年' },
      { id: `${categoryPrefix}-${year}-permanent`, label: '永久' }
    ]
  }));
};

const archiveCategories: ArchiveCategory[] = [
  {
    id: 'all',
    label: '全部',
    expanded: true,
    children: [
      {
        id: 'document',
        label: '文书',
        children: generateYearCategories('doc')
      },
      {
        id: 'personnel',
        label: '人事',
        children: generateYearCategories('personnel')
      },
      {
        id: 'accounting',
        label: '会计',
        children: generateYearCategories('accounting')
      },
      {
        id: 'construction',
        label: '基建',
        children: generateYearCategories('construction')
      },
      {
        id: 'photo',
        label: '照片',
        children: generateYearCategories('photo')
      }
    ]
  }
];

interface ArchiveRecord {
  id: number;
  title: string;
  type: string;
  createTime: string;
  status: 'pending' | 'archived';
}

const mockRecords: ArchiveRecord[] = [
  {
    id: 1,
    title: '浙江省教育厅关于省政协十二届五次会议第604号提案的答复',
    type: '文书',
    createTime: '2025.06.23 09:48',
    status: 'pending'
  },
  {
    id: 2,
    title: '浙江省教育厅关于我省高校第五轮学科评估结果相关情况的函',
    type: '文书',
    createTime: '2025.06.23 09:48',
    status: 'archived'
  },
  {
    id: 3,
    title: '中共浙江省教育厅党组关于2022年度政治生态建设自评自查情况的报告',
    type: '人事',
    createTime: '2025.06.22 14:30',
    status: 'pending'
  }
];

// 手工录入弹窗组件
interface ManualEntryDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  editData?: ArchiveRecord;
}

function ManualEntryDialog({ open, onClose, onSubmit, editData }: ManualEntryDialogProps) {
  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[856px] max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-medium">{editData ? '编辑档案' : '手工录入'}</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">档号：</label>
              <Input defaultValue={editData ? `ZJS-${getCurrentYear()}-${String(editData.id).padStart(4, '0')}` : `ZJS-${getCurrentYear()}-0001`} className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">全宗号：</label>
              <Input defaultValue="ZJS" className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">年度：</label>
              <Select defaultValue={getCurrentYear().toString()}>
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {generateYearsList().map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">保管期限：</label>
              <Select defaultValue="30">
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10年</SelectItem>
                  <SelectItem value="30">30年</SelectItem>
                  <SelectItem value="permanent">永久</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">组织机构：</label>
              <Select defaultValue="office">
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="office">办公室</SelectItem>
                  <SelectItem value="finance">财务处</SelectItem>
                  <SelectItem value="personnel">人事处</SelectItem>
                  <SelectItem value="academic">学术处</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">序号：</label>
              <div className="flex items-center border rounded flex-1">
                <Button variant="ghost" size="sm" className="px-2">-</Button>
                <span className="flex-1 text-center">{editData?.id || 1}</span>
                <Button variant="ghost" size="sm" className="px-2">+</Button>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">正题名：</label>
            <Input defaultValue={editData?.title || ''} placeholder="请输入档案标题" className="flex-1" />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">责任人：</label>
              <Input defaultValue="浙江省教育厅" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">文件编号：</label>
              <Input defaultValue={editData ? `浙教办[${getCurrentYear()}]${editData.id}号` : ''} placeholder="请输入" className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">形成时间：</label>
              <Input defaultValue={editData?.createTime?.split(' ')[0] || ''} placeholder="请输入" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">文件页数：</label>
              <Input defaultValue="5" placeholder="请输入" className="flex-1" />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">主题词：</label>
              <Input defaultValue="教育,政策" placeholder="请输入，多个主题词用逗号分隔" className="flex-1" />
            </div>
            <div className="flex items-center gap-4">
              <label className="text-sm text-gray-600 w-24 text-right">档案类型：</label>
              <Select defaultValue={editData?.type || 'document'}>
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="document">文书</SelectItem>
                  <SelectItem value="personnel">人事</SelectItem>
                  <SelectItem value="accounting">会计</SelectItem>
                  <SelectItem value="construction">基建</SelectItem>
                  <SelectItem value="photo">照片</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">备注：</label>
            <Input placeholder="请输入备注信息" className="flex-1" />
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-600 w-24 text-right">附件：</label>
            <Button className="bg-[#2a78ff] text-white">
              <Plus className="w-4 h-4" />
              上传附件
            </Button>
          </div>
        </div>

        <div className="flex justify-end gap-4 p-6 border-t">
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button className="bg-[#2a78ff]" onClick={() => onSubmit({})}>
            {editData ? '保存' : '确定'}
          </Button>
        </div>
      </div>
    </div>
  );
}

// 导入弹窗组件
interface ImportDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (file: File) => void;
}

function ImportDialog({ open, onClose, onSubmit }: ImportDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  if (!open) return null;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleSubmit = () => {
    if (selectedFile) {
      onSubmit(selectedFile);
      setSelectedFile(null);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[500px]">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-medium">导入档案</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="p-6 space-y-4">
          <div className="text-sm text-gray-600 mb-4">
            请选择要导入的Excel文件，支持.xlsx、.xls格式
          </div>
          
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-600 mb-2">拖拽文件到这里，或</div>
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileSelect}
              className="hidden"
              id="file-input"
            />
            <label htmlFor="file-input">
              <Button variant="outline" className="cursor-pointer">
                选择文件
              </Button>
            </label>
          </div>
          
          {selectedFile && (
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded">
              <div className="flex-1">
                <div className="font-medium">{selectedFile.name}</div>
                <div className="text-sm text-gray-500">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedFile(null)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-4 p-6 border-t">
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button 
            className="bg-[#2a78ff]" 
            disabled={!selectedFile}
            onClick={handleSubmit}
          >
            开始导入
          </Button>
        </div>
      </div>
    </div>
  );
}

interface ManualEntryProps {
  onViewArchive?: (archiveId: string) => void;
}

export function ManualEntry({ onViewArchive }: ManualEntryProps) {
  const [categories, setCategories] = useState(archiveCategories);
  const [records, setRecords] = useState(mockRecords);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [showManualEntryDialog, setShowManualEntryDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [editingRecord, setEditingRecord] = useState<ArchiveRecord | undefined>();

  const toggleCategory = (categoryId: string) => {
    const updateExpanded = (items: ArchiveCategory[]): ArchiveCategory[] => {
      return items.map(item => {
        if (item.id === categoryId) {
          return { ...item, expanded: !item.expanded };
        }
        if (item.children) {
          return { ...item, children: updateExpanded(item.children) };
        }
        return item;
      });
    };
    setCategories(updateExpanded(categories));
  };

  const renderCategoryTree = (items: ArchiveCategory[], level = 0) => {
    return items.map(item => (
      <div key={item.id}>
        <div 
          className={`flex items-center justify-between h-11 px-5 cursor-pointer hover:bg-gray-50 ${
            item.id === 'all' ? 'bg-[#f5f7fa] border-l-4 border-l-[#2a78ff] text-[#2a78ff]' : ''
          }`}
          style={{ paddingLeft: `${20 + level * 16}px` }}
          onClick={() => item.children && toggleCategory(item.id)}
        >
          <div className="flex items-center gap-2">
            {level > 0 && (
              <div className="w-4 h-4 flex items-center justify-center">
                {item.children ? (
                  item.expanded ? <ChevronDown className="w-3 h-3" /> : <ChevronDown className="w-3 h-3 rotate-180" />
                ) : (
                  <div className="w-2 h-2 bg-[#2a78ff] rounded-full"></div>
                )}
              </div>
            )}
            <span className="text-sm">{item.label}</span>
          </div>
          {item.children && level === 0 && (
            <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${item.expanded ? '' : 'rotate-180'}`} />
          )}
        </div>
        
        {item.children && item.expanded && (
          <div>
            {renderCategoryTree(item.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedItems(checked ? records.map(item => item.id) : []);
  };

  const handleSelectItem = (id: number, checked: boolean) => {
    setSelectedItems(prev => 
      checked ? [...prev, id] : prev.filter(itemId => itemId !== id)
    );
  };

  // 手工录入
  const handleManualEntry = () => {
    setEditingRecord(undefined);
    setShowManualEntryDialog(true);
  };

  // 导入
  const handleImport = () => {
    setShowImportDialog(true);
  };

  // 下载模版
  const handleDownloadTemplate = () => {
    // 模拟下载Excel模版文件
    const link = document.createElement('a');
    link.href = '#'; // 实际应用中这里应该是模版文件的URL
    link.download = '档案导入模版.xlsx';
    link.click();
    alert('模版下载已开始');
  };

  // 删除
  const handleDelete = () => {
    if (selectedItems.length === 0) return;
    
    if (confirm(`确定要删除选中的 ${selectedItems.length} 条记录吗？`)) {
      setRecords(prev => prev.filter(record => !selectedItems.includes(record.id)));
      setSelectedItems([]);
      alert('删除成功');
    }
  };

  // 整理（编辑）
  const handleEdit = (record: ArchiveRecord) => {
    setEditingRecord(record);
    setShowManualEntryDialog(true);
  };

  // 删除单条记录
  const handleDeleteSingle = (record: ArchiveRecord) => {
    if (confirm(`确定要删除"${record.title}"吗？`)) {
      setRecords(prev => prev.filter(r => r.id !== record.id));
      alert('删除成功');
    }
  };

  // 提交手工录入/编辑
  const handleManualEntrySubmit = (data: any) => {
    if (editingRecord) {
      // 编辑现有记录
      setRecords(prev => prev.map(record => 
        record.id === editingRecord.id 
          ? { ...record, ...data, status: 'archived' as const }
          : record
      ));
      alert('编辑成功');
    } else {
      // 新建记录
      const newRecord: ArchiveRecord = {
        id: Math.max(...records.map(r => r.id)) + 1,
        title: data.title || '新建档案记录',
        type: data.type || '文书',
        createTime: new Date().toLocaleDateString('zh-CN').replace(/\//g, '.') + ' ' + new Date().toLocaleTimeString('zh-CN', { hour12: false }),
        status: 'pending'
      };
      setRecords(prev => [...prev, newRecord]);
      alert('录入成功');
    }
    setShowManualEntryDialog(false);
    setEditingRecord(undefined);
  };

  // 处理导入
  const handleImportSubmit = (file: File) => {
    // 模拟导入处理
    console.log('导入文件:', file.name);
    alert(`文件 "${file.name}" 导入成功，共导入 3 条记录`);
    
    // 模拟添加导入的记录
    const importedRecords: ArchiveRecord[] = [
      {
        id: Math.max(...records.map(r => r.id)) + 1,
        title: '导入档案记录1',
        type: '文书',
        createTime: new Date().toLocaleDateString('zh-CN').replace(/\//g, '.') + ' ' + new Date().toLocaleTimeString('zh-CN', { hour12: false }),
        status: 'pending'
      },
      {
        id: Math.max(...records.map(r => r.id)) + 2,
        title: '导入档案记录2',
        type: '人事',
        createTime: new Date().toLocaleDateString('zh-CN').replace(/\//g, '.') + ' ' + new Date().toLocaleTimeString('zh-CN', { hour12: false }),
        status: 'pending'
      }
    ];
    
    setRecords(prev => [...prev, ...importedRecords]);
  };

  return (
    <div className="flex-1 bg-gray-100 p-4">
      {/* Search Form */}
      <div className="bg-white rounded p-4 mb-4">
        <div className="flex items-center gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">年份：</span>
            <Select defaultValue={getCurrentYear().toString()}>
              <SelectTrigger className="w-44">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {generateYearsList().map(year => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">创建日期：</span>
            <div className="flex items-center gap-2 border rounded px-3 py-2 w-56">
              <Calendar className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-700">开始日期</span>
              <span className="text-gray-400">-</span>
              <span className="text-sm text-gray-700">结束日期</span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-700">标题：</span>
            <Input placeholder="请输入" className="w-64" />
          </div>

          <Button className="bg-[#2a78ff]">查询</Button>
          <Button variant="outline">重置</Button>
        </div>
      </div>

      <div className="flex gap-4 h-[calc(100vh-240px)]">
        {/* Category Sidebar */}
        <div className="w-52 bg-white rounded overflow-hidden">
          <div className="p-3 border-b">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input placeholder="请搜索" className="pl-10" />
            </div>
          </div>
          
          <div className="h-full overflow-y-auto">
            {renderCategoryTree(categories)}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white rounded border-l">
          <div className="p-4">
            {/* Action Buttons */}
            <div className="flex gap-4 mb-4">
              <Button className="bg-[#2a78ff] text-white" onClick={handleManualEntry}>
                <Plus className="w-4 h-4" />
                手工录入
              </Button>
              <Button className="bg-[#2a78ff] text-white" onClick={handleImport}>
                <Upload className="w-4 h-4" />
                导入
              </Button>
              <Button className="bg-[#2a78ff] text-white" onClick={handleDownloadTemplate}>
                <Download className="w-4 h-4" />
                下载模版
              </Button>
              <Button
                className={`${selectedItems.length > 0 ? 'bg-red-600' : 'bg-gray-400'} text-white`}
                disabled={selectedItems.length === 0}
                onClick={handleDelete}
              >
                <Trash2 className="w-4 h-4" />
                删除
              </Button>
            </div>

            {/* Table */}
            <div className="border rounded overflow-hidden">
              {/* Header */}
              <div className="bg-gray-50 flex items-center text-sm font-medium text-gray-600 border-b">
                <div className="w-16 p-4 border-r">
                  <Checkbox
                    checked={selectedItems.length === records.length && records.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </div>
                <div className="w-16 p-4 border-r">编号</div>
                <div className="flex-1 p-4 border-r">主题名</div>
                <div className="w-24 p-4 border-r">档案类型</div>
                <div className="w-40 p-4 border-r">创建时间</div>
                <div className="w-24 p-4 border-r">状态</div>
                <div className="w-32 p-4">操作</div>
              </div>

              {/* Rows */}
              {records.map((record, index) => (
                <div key={record.id} className={`flex items-center text-sm ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} border-b last:border-b-0`}>
                  <div className="w-16 p-4 border-r">
                    <Checkbox
                      checked={selectedItems.includes(record.id)}
                      onCheckedChange={(checked) => handleSelectItem(record.id, checked as boolean)}
                    />
                  </div>
                  <div className="w-16 p-4 border-r text-gray-900">{record.id}</div>
                  <div className="flex-1 p-4 border-r text-gray-900 truncate">
                    <button 
                      className="text-blue-600 hover:text-blue-800 hover:underline text-left"
                      onClick={() => onViewArchive?.(record.id.toString())}
                    >
                      {record.title}
                    </button>
                  </div>
                  <div className="w-24 p-4 border-r text-gray-900">{record.type}</div>
                  <div className="w-40 p-4 border-r text-gray-900">{record.createTime}</div>
                  <div className="w-24 p-4 border-r">
                    <Badge className={record.status === 'archived' ? 'bg-green-100 text-green-600 border-green-200' : 'bg-orange-100 text-orange-600 border-orange-200'}>
                      {record.status === 'archived' ? '已归档' : '待归档'}
                    </Badge>
                  </div>
                  <div className="w-32 p-4">
                    <div className="flex gap-2">
                      <Button 
                        variant="link" 
                        size="sm" 
                        className="text-[#2a78ff] p-0"
                        onClick={() => handleEdit(record)}
                      >
                        <Edit className="w-4 h-4 mr-1" />
                        整理
                      </Button>
                      {record.status === 'pending' && (
                        <Button 
                          variant="link" 
                          size="sm" 
                          className="text-red-600 p-0"
                          onClick={() => handleDeleteSingle(record)}
                        >
                          <Trash2 className="w-4 h-4 mr-1" />
                          删除
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-end gap-4 mt-4 text-sm text-gray-600">
              <span>共 {records.length} 条</span>
              <div className="flex items-center gap-2">
                <span>每页</span>
                <Select defaultValue="10">
                  <SelectTrigger className="w-16 h-7">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span>条</span>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled>
                  上一页
                </Button>
                <span className="text-blue-600">1</span>
                <Button variant="outline" size="sm" disabled>
                  下一页
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <span>前往</span>
                <Input className="w-12 h-7" defaultValue="1" />
                <span>页</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 手工录入/编辑弹窗 */}
      <ManualEntryDialog 
        open={showManualEntryDialog}
        onClose={() => {
          setShowManualEntryDialog(false);
          setEditingRecord(undefined);
        }}
        onSubmit={handleManualEntrySubmit}
        editData={editingRecord}
      />

      {/* 导入弹窗 */}
      <ImportDialog 
        open={showImportDialog}
        onClose={() => setShowImportDialog(false)}
        onSubmit={handleImportSubmit}
      />
    </div>
  );
}