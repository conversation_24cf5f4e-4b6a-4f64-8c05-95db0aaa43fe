import { useState } from 'react';
import { Plus, Edit, Trash2, Save, X, <PERSON>ting<PERSON>, Users, Shield, FolderTree } from 'lucide-react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Badge } from './ui/badge';
import { Checkbox } from './ui/checkbox';

// 档案分类数据
interface ArchiveCategory {
  id: string;
  code: string;
  name: string;
  parentId?: string;
  retentionPeriod: string;
  description: string;
  status: 'active' | 'inactive';
}

const mockCategories: ArchiveCategory[] = [
  { id: '1', code: 'WS', name: '文书档案', retentionPeriod: '永久', description: '机关文书档案', status: 'active' },
  { id: '2', code: 'RS', name: '人事档案', retentionPeriod: '永久', description: '人事管理档案', status: 'active' },
  { id: '3', code: 'CW', name: '财务档案', retentionPeriod: '30年', description: '财务管理档案', status: 'active' },
  { id: '4', code: 'JJ', name: '基建档案', retentionPeriod: '永久', description: '基础建设档案', status: 'active' },
  { id: '5', code: 'ZP', name: '照片档案', retentionPeriod: '永久', description: '重要活动照片', status: 'active' }
];

// 全宗号数据
interface FondsNumber {
  id: string;
  code: string;
  name: string;
  unit: string;
  startYear: string;
  endYear: string;
  description: string;
  status: 'active' | 'inactive';
}

const mockFondsNumbers: FondsNumber[] = [
  { id: '1', code: 'ZJS', name: '浙江省教育厅', unit: '浙江省教育厅', startYear: '1978', endYear: '', description: '省教育厅主要全宗', status: 'active' },
  { id: '2', code: 'ZJSXXZX', name: '浙江省教育信息中心', unit: '浙江省教育信息中心', startYear: '2005', endYear: '', description: '教育信息化建设', status: 'active' },
  { id: '3', code: 'ZJSKSZX', name: '浙江省考试院', unit: '浙江省考试院', startYear: '1980', endYear: '', description: '教育考试管理', status: 'active' }
];

// 用户数据
interface User {
  id: string;
  username: string;
  realName: string;
  department: string;
  role: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive';
  lastLogin: string;
}

const mockUsers: User[] = [
  { id: '1', username: 'admin', realName: '系统管理员', department: '信息中心', role: '超级管理员', email: '<EMAIL>', phone: '0571-12345678', status: 'active', lastLogin: '2025.06.23 09:30' },
  { id: '2', username: 'archivist', realName: '档案管理员', department: '办公室', role: '档案管理员', email: '<EMAIL>', phone: '0571-12345679', status: 'active', lastLogin: '2025.06.23 08:45' },
  { id: '3', username: 'user001', realName: '张三', department: '高等教育处', role: '普通用户', email: '<EMAIL>', phone: '0571-12345680', status: 'active', lastLogin: '2025.06.22 16:20' },
  { id: '4', username: 'user002', realName: '李四', department: '发展规划处', role: '普通用户', email: '<EMAIL>', phone: '0571-12345681', status: 'active', lastLogin: '2025.06.22 14:30' }
];

// 权限数据
interface Permission {
  id: string;
  name: string;
  code: string;
  module: string;
  description: string;
}

const mockPermissions: Permission[] = [
  { id: '1', name: '档案查看', code: 'archive:view', module: '档案管理', description: '查看档案信息' },
  { id: '2', name: '档案编辑', code: 'archive:edit', module: '档案管理', description: '编辑档案信息' },
  { id: '3', name: '档案删除', code: 'archive:delete', module: '档案管理', description: '删除档案' },
  { id: '4', name: '档案下载', code: 'archive:download', module: '档案管理', description: '下载档案文件' },
  { id: '5', name: '用户管理', code: 'user:manage', module: '系统管理', description: '管理用户账户' },
  { id: '6', name: '系统配置', code: 'system:config', module: '系统管理', description: '系统参数配置' }
];

const systemTabs = [
  { id: 'categories', label: '档案分类', icon: FolderTree },
  { id: 'fonds', label: '全宗号管理', icon: Settings },
  { id: 'users', label: '用户管理', icon: Users },
  { id: 'permissions', label: '权限管理', icon: Shield }
];

export function SystemManagement() {
  const [activeTab, setActiveTab] = useState('categories');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);

  const renderCategoriesTab = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">档案分类管理</h3>
        <Button className="bg-[#2a78ff] flex items-center gap-1.5" onClick={() => setShowAddDialog(true)}>
          <Plus className="w-4 h-4" />
          新增分类
        </Button>
      </div>

      <div className="bg-white border rounded overflow-hidden">
        <div className="bg-gray-50 flex items-center text-sm font-medium text-gray-600 border-b">
          <div className="w-24 p-4 border-r">分类代码</div>
          <div className="flex-1 p-4 border-r">分类名称</div>
          <div className="w-32 p-4 border-r">保管期限</div>
          <div className="w-48 p-4 border-r">描述</div>
          <div className="w-20 p-4 border-r">状态</div>
          <div className="w-32 p-4">操作</div>
        </div>

        {mockCategories.map((category, index) => (
          <div key={category.id} className={`flex items-center text-sm ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} border-b last:border-b-0`}>
            <div className="w-24 p-4 border-r font-mono">{category.code}</div>
            <div className="flex-1 p-4 border-r">{category.name}</div>
            <div className="w-32 p-4 border-r">{category.retentionPeriod}</div>
            <div className="w-48 p-4 border-r truncate">{category.description}</div>
            <div className="w-20 p-4 border-r">
              <Badge className={category.status === 'active' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}>
                {category.status === 'active' ? '启用' : '禁用'}
              </Badge>
            </div>
            <div className="w-32 p-4">
              <div className="flex gap-2">
                <Button variant="link" size="sm" className="text-[#2a78ff] p-0">
                  <Edit className="w-4 h-4" />
                  编辑
                </Button>
                <Button variant="link" size="sm" className="text-red-600 p-0">
                  <Trash2 className="w-4 h-4" />
                  删除
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderFondsTab = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">全宗号管理</h3>
        <Button className="bg-[#2a78ff] flex items-center gap-1.5" onClick={() => setShowAddDialog(true)}>
          <Plus className="w-4 h-4" />
          新增全宗号
        </Button>
      </div>

      <div className="bg-white border rounded overflow-hidden">
        <div className="bg-gray-50 flex items-center text-sm font-medium text-gray-600 border-b">
          <div className="w-32 p-4 border-r">全宗号</div>
          <div className="flex-1 p-4 border-r">全宗名称</div>
          <div className="w-48 p-4 border-r">责任单位</div>
          <div className="w-32 p-4 border-r">起始年度</div>
          <div className="w-32 p-4 border-r">结束年度</div>
          <div className="w-20 p-4 border-r">状态</div>
          <div className="w-32 p-4">操作</div>
        </div>

        {mockFondsNumbers.map((fonds, index) => (
          <div key={fonds.id} className={`flex items-center text-sm ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} border-b last:border-b-0`}>
            <div className="w-32 p-4 border-r font-mono">{fonds.code}</div>
            <div className="flex-1 p-4 border-r">{fonds.name}</div>
            <div className="w-48 p-4 border-r">{fonds.unit}</div>
            <div className="w-32 p-4 border-r">{fonds.startYear}</div>
            <div className="w-32 p-4 border-r">{fonds.endYear || '至今'}</div>
            <div className="w-20 p-4 border-r">
              <Badge className={fonds.status === 'active' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}>
                {fonds.status === 'active' ? '启用' : '禁用'}
              </Badge>
            </div>
            <div className="w-32 p-4">
              <div className="flex gap-2">
                <Button variant="link" size="sm" className="text-[#2a78ff] p-0">
                  <Edit className="w-4 h-4" />
                  编辑
                </Button>
                <Button variant="link" size="sm" className="text-red-600 p-0">
                  <Trash2 className="w-4 h-4" />
                  删除
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderUsersTab = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">用户管理</h3>
        <Button className="bg-[#2a78ff] flex items-center gap-1.5" onClick={() => setShowAddDialog(true)}>
          <Plus className="w-4 h-4" />
          新增用户
        </Button>
      </div>

      <div className="bg-white border rounded overflow-hidden">
        <div className="bg-gray-50 flex items-center text-sm font-medium text-gray-600 border-b">
          <div className="w-32 p-4 border-r">用户名</div>
          <div className="w-24 p-4 border-r">姓名</div>
          <div className="w-32 p-4 border-r">部门</div>
          <div className="w-24 p-4 border-r">角色</div>
          <div className="w-48 p-4 border-r">邮箱</div>
          <div className="w-32 p-4 border-r">最后登录</div>
          <div className="w-20 p-4 border-r">状态</div>
          <div className="w-32 p-4">操作</div>
        </div>

        {mockUsers.map((user, index) => (
          <div key={user.id} className={`flex items-center text-sm ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} border-b last:border-b-0`}>
            <div className="w-32 p-4 border-r font-mono">{user.username}</div>
            <div className="w-24 p-4 border-r">{user.realName}</div>
            <div className="w-32 p-4 border-r">{user.department}</div>
            <div className="w-24 p-4 border-r">
              <Badge variant="outline" className={
                user.role === '超级管理员' ? 'text-red-600' :
                user.role === '档案管理员' ? 'text-blue-600' : 'text-gray-600'
              }>
                {user.role}
              </Badge>
            </div>
            <div className="w-48 p-4 border-r truncate">{user.email}</div>
            <div className="w-32 p-4 border-r text-xs">{user.lastLogin}</div>
            <div className="w-20 p-4 border-r">
              <Badge className={user.status === 'active' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}>
                {user.status === 'active' ? '正常' : '禁用'}
              </Badge>
            </div>
            <div className="w-32 p-4">
              <div className="flex gap-2">
                <Button variant="link" size="sm" className="text-[#2a78ff] p-0">
                  <Edit className="w-4 h-4" />
                  编辑
                </Button>
                <Button variant="link" size="sm" className="text-orange-600 p-0">
                  重置密码
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderPermissionsTab = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">权限管理</h3>
        <Button className="bg-[#2a78ff] flex items-center gap-1.5" onClick={() => setShowAddDialog(true)}>
          <Plus className="w-4 h-4" />
          新增权限
        </Button>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>权限列表</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {mockPermissions.map((permission) => (
                <div key={permission.id} className="flex items-center justify-between p-3 border rounded">
                  <div>
                    <div className="font-medium text-sm">{permission.name}</div>
                    <div className="text-xs text-gray-500">{permission.code}</div>
                    <div className="text-xs text-gray-400">{permission.description}</div>
                  </div>
                  <Badge variant="outline">{permission.module}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>角色权限配置</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">选择角色：</label>
                <Select defaultValue="admin">
                  <SelectTrigger className="w-full mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">超级管理员</SelectItem>
                    <SelectItem value="archivist">档案管理员</SelectItem>
                    <SelectItem value="user">普通用户</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">权限分配：</label>
                <div className="space-y-2">
                  {mockPermissions.map((permission) => (
                    <div key={permission.id} className="flex items-center space-x-2">
                      <Checkbox id={permission.id} defaultChecked={permission.module === '档案管理'} />
                      <label htmlFor={permission.id} className="text-sm">
                        {permission.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Button className="w-full bg-[#2a78ff] flex items-center justify-center gap-1.5">
                <Save className="w-4 h-4" />
                保存配置
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="flex-1 bg-gray-100 p-4">
      <div className="bg-white rounded">
        <div className="p-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              {systemTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </TabsTrigger>
                );
              })}
            </TabsList>

            <div className="mt-6">
              <TabsContent value="categories">
                {renderCategoriesTab()}
              </TabsContent>
              <TabsContent value="fonds">
                {renderFondsTab()}
              </TabsContent>
              <TabsContent value="users">
                {renderUsersTab()}
              </TabsContent>
              <TabsContent value="permissions">
                {renderPermissionsTab()}
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}