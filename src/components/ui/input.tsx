import * as React from "react";

import { cn } from "./utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-gray-400 selection:bg-[#2a78ff] selection:text-white dark:bg-input/30 border-gray-200 flex h-9 w-full min-w-0 rounded-md border px-3 py-1 text-base bg-white transition-all outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium md:text-sm",
        "focus-visible:border-[#2a78ff] focus-visible:ring-[#2a78ff]/20 focus-visible:ring-[3px] focus-visible:bg-white",
        "hover:border-[#2a78ff]/40",
        "disabled:pointer-events-none disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:border-gray-200",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className,
      )}
      {...props}
    />
  );
}

export { Input };
