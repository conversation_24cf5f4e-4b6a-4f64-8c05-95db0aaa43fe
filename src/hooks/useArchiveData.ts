import { useState, useEffect, useCallback } from 'react';
import { archiveService, ArchiveItem, ArchiveListParams, ArchiveListResponse } from '../services/archiveService';

interface UseArchiveDataOptions {
  initialStatus?: string;
  pageSize?: number;
}

interface UseArchiveDataReturn {
  data: ArchiveItem[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  pageSize: number;
  status: string;
  searchQuery: string;
  setStatus: (status: string) => void;
  setSearchQuery: (query: string) => void;
  setPage: (page: number) => void;
  refetch: () => void;
  updateArchiveStatus: (ids: number[], status: ArchiveItem['status']) => Promise<void>;
  deleteArchives: (ids: number[]) => Promise<void>;
  addArchive: (archiveData: Omit<ArchiveItem, 'id'>) => Promise<void>;
}

export function useArchiveData(options: UseArchiveDataOptions = {}): UseArchiveDataReturn {
  const { initialStatus = 'all', pageSize = 10 } = options;

  const [data, setData] = useState<ArchiveItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState(initialStatus);
  const [searchQuery, setSearchQuery] = useState('');

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: ArchiveListParams = {
        status: status === 'all' ? undefined : status,
        page,
        pageSize,
        searchQuery: searchQuery.trim() || undefined
      };

      const response: ArchiveListResponse = await archiveService.getArchiveList(params);
      
      setData(response.data);
      setTotal(response.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  }, [status, page, pageSize, searchQuery]);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Reset page when status or search query changes
  useEffect(() => {
    if (page !== 1) {
      setPage(1);
    }
  }, [status, searchQuery]);

  const handleSetStatus = useCallback((newStatus: string) => {
    setStatus(newStatus);
  }, []);

  const handleSetSearchQuery = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleSetPage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleUpdateArchiveStatus = useCallback(async (ids: number[], newStatus: ArchiveItem['status']) => {
    try {
      setLoading(true);
      await archiveService.updateArchiveStatus(ids, newStatus);
      // Refetch data to get updated results
      await fetchData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update archive status');
    } finally {
      setLoading(false);
    }
  }, [fetchData]);

  const handleDeleteArchives = useCallback(async (ids: number[]) => {
    try {
      setLoading(true);
      await archiveService.deleteArchives(ids);
      // Refetch data to get updated results
      await fetchData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete archives');
    } finally {
      setLoading(false);
    }
  }, [fetchData]);

  const handleAddArchive = useCallback(async (archiveData: Omit<ArchiveItem, 'id'>) => {
    try {
      setLoading(true);
      await archiveService.addArchive(archiveData);
      // Refetch data to get updated results
      await fetchData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add archive');
    } finally {
      setLoading(false);
    }
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    total,
    page,
    pageSize,
    status,
    searchQuery,
    setStatus: handleSetStatus,
    setSearchQuery: handleSetSearchQuery,
    setPage: handleSetPage,
    refetch: fetchData,
    updateArchiveStatus: handleUpdateArchiveStatus,
    deleteArchives: handleDeleteArchives,
    addArchive: handleAddArchive
  };
}
