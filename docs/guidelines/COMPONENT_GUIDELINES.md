# 组件开发指南

## 🧩 组件分类和规范

### 1. 组件分类

#### UI 基础组件 (`src/components/ui/`)
- **用途**: 可复用的基础 UI 组件
- **特点**: 无业务逻辑，高度可配置
- **示例**: Button, Input, Dialog, Card

#### 业务组件 (`src/components/`)
- **用途**: 包含业务逻辑的功能组件
- **特点**: 与具体业务场景相关
- **示例**: ArchiveCollection, SystemManagement, Login

#### 布局组件
- **用途**: 页面结构和布局
- **特点**: 负责页面整体结构
- **示例**: Layout, Sidebar

### 2. 组件命名规范

```typescript
// ✅ 正确的组件命名
export function ArchiveCollection() {}     // 业务组件 - PascalCase
export function Button() {}               // UI 组件 - PascalCase
export function useArchiveData() {}       // 自定义 Hook - camelCase

// ❌ 错误的命名
export function archiveCollection() {}    // 小写开头
export function Archive_Collection() {}   // 下划线分隔
```

## 📝 组件开发模板

### 1. 基础组件模板

```typescript
import { ReactNode } from 'react';
import { cn } from './utils';

interface ComponentProps {
  children?: ReactNode;
  className?: string;
  variant?: 'default' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
}

export function Component({
  children,
  className,
  variant = 'default',
  size = 'md',
  disabled = false,
  onClick,
}: ComponentProps) {
  return (
    <div
      className={cn(
        // 基础样式
        'inline-flex items-center justify-center rounded-md font-medium',
        // 变体样式
        {
          'bg-primary text-primary-foreground': variant === 'default',
          'bg-secondary text-secondary-foreground': variant === 'secondary',
        },
        // 尺寸样式
        {
          'h-8 px-3 text-sm': size === 'sm',
          'h-9 px-4 text-base': size === 'md',
          'h-10 px-6 text-lg': size === 'lg',
        },
        // 状态样式
        {
          'opacity-50 cursor-not-allowed': disabled,
          'hover:opacity-90 cursor-pointer': !disabled,
        },
        className
      )}
      onClick={disabled ? undefined : onClick}
    >
      {children}
    </div>
  );
}
```

### 2. 业务组件模板

```typescript
import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

interface BusinessComponentProps {
  title: string;
  data?: any[];
  onAction?: (id: string) => void;
  loading?: boolean;
}

export function BusinessComponent({
  title,
  data = [],
  onAction,
  loading = false,
}: BusinessComponentProps) {
  const [localState, setLocalState] = useState<string>('');

  useEffect(() => {
    // 副作用逻辑
  }, []);

  const handleLocalAction = (id: string) => {
    // 本地处理逻辑
    setLocalState(id);
    
    // 调用父组件回调
    onAction?.(id);
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {data.map((item) => (
          <div key={item.id}>
            {/* 渲染逻辑 */}
            <Button onClick={() => handleLocalAction(item.id)}>
              Action
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
```

## 🎨 样式开发规范

### 1. Tailwind CSS 使用规范

```typescript
// ✅ 推荐的样式写法
<div className="flex items-center gap-4 p-6 bg-white rounded-lg shadow-md">

// ✅ 使用设计令牌
<div className="bg-primary text-primary-foreground">

// ✅ 响应式设计
<div className="w-full md:w-1/2 lg:w-1/3">

// ❌ 避免内联样式
<div style={{ display: 'flex', padding: '24px' }}>

// ❌ 避免过长的类名
<div className="flex items-center justify-center w-full h-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold text-xl rounded-lg shadow-2xl hover:shadow-3xl transition-all duration-300">
```

### 2. 条件样式处理

```typescript
import { cn } from './ui/utils';

// ✅ 使用 cn 函数处理条件样式
<div
  className={cn(
    'base-styles',
    {
      'active-styles': isActive,
      'disabled-styles': disabled,
    },
    className // 允许外部覆盖
  )}
>

// ✅ 复杂条件样式
<div
  className={cn(
    'px-4 py-2 rounded',
    variant === 'primary' && 'bg-blue-500 text-white',
    variant === 'secondary' && 'bg-gray-200 text-gray-800',
    size === 'sm' && 'text-sm',
    size === 'lg' && 'text-lg',
    disabled && 'opacity-50 cursor-not-allowed'
  )}
>
```

## 🔄 状态管理规范

### 1. 本地状态管理

```typescript
// ✅ 简单状态
const [isOpen, setIsOpen] = useState(false);
const [inputValue, setInputValue] = useState('');

// ✅ 复杂状态对象
interface FormState {
  name: string;
  email: string;
  errors: Record<string, string>;
}

const [formState, setFormState] = useState<FormState>({
  name: '',
  email: '',
  errors: {},
});

// ✅ 状态更新
const updateFormField = (field: keyof FormState, value: string) => {
  setFormState(prev => ({
    ...prev,
    [field]: value,
    errors: {
      ...prev.errors,
      [field]: '', // 清除错误
    },
  }));
};
```

### 2. 副作用管理

```typescript
// ✅ 数据获取
useEffect(() => {
  const fetchData = async () => {
    try {
      setLoading(true);
      const data = await api.getData();
      setData(data);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  fetchData();
}, []);

// ✅ 清理副作用
useEffect(() => {
  const timer = setInterval(() => {
    // 定时器逻辑
  }, 1000);

  return () => clearInterval(timer);
}, []);
```

## 🎯 事件处理规范

### 1. 事件处理函数命名

```typescript
// ✅ 标准命名模式
const handleClick = () => {};
const handleSubmit = (e: FormEvent) => {};
const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {};
const handleModalOpen = () => {};
const handleDataLoad = () => {};

// ❌ 避免的命名
const click = () => {};
const onSubmit = () => {}; // 这应该是 prop 名称
const changeInput = () => {};
```

### 2. 事件处理最佳实践

```typescript
// ✅ 表单提交处理
const handleSubmit = (e: FormEvent) => {
  e.preventDefault();
  
  // 验证
  if (!validateForm()) {
    return;
  }
  
  // 处理提交
  onSubmit?.(formData);
};

// ✅ 异步事件处理
const handleAsyncAction = async () => {
  try {
    setLoading(true);
    await performAction();
    setSuccess(true);
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
};

// ✅ 防抖处理
const debouncedSearch = useMemo(
  () => debounce((query: string) => {
    onSearch(query);
  }, 300),
  [onSearch]
);
```

## 🧪 组件测试规范

### 1. 基础测试模板

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Component } from './Component';

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component title="Test" />);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Component onClick={handleClick} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies custom className', () => {
    render(<Component className="custom-class" />);
    expect(screen.getByRole('button')).toHaveClass('custom-class');
  });
});
```

### 2. 异步测试

```typescript
import { waitFor } from '@testing-library/react';

it('loads data asynchronously', async () => {
  const mockData = [{ id: 1, name: 'Test' }];
  jest.spyOn(api, 'getData').mockResolvedValue(mockData);

  render(<DataComponent />);

  await waitFor(() => {
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});
```

## 📚 文档规范

### 1. 组件文档模板

```typescript
/**
 * 档案收集组件
 * 
 * 用于处理电子档案的采集和展示，支持搜索、过滤和批量操作。
 * 
 * @example
 * ```tsx
 * <ArchiveCollection
 *   onViewArchive={(id) => console.log('View:', id)}
 *   searchQuery="关键词"
 *   filters={[{ type: 'category', value: 'document' }]}
 * />
 * ```
 */
export function ArchiveCollection({
  onViewArchive,
  searchQuery,
  filters = [],
}: ArchiveCollectionProps) {
  // 实现
}
```

### 2. Props 文档

```typescript
interface ComponentProps {
  /** 组件标题 */
  title: string;
  
  /** 可选的描述文本 */
  description?: string;
  
  /** 点击事件回调函数 */
  onClick?: (event: MouseEvent) => void;
  
  /** 组件变体
   * @default 'default'
   */
  variant?: 'default' | 'primary' | 'secondary';
  
  /** 是否禁用组件
   * @default false
   */
  disabled?: boolean;
}
```

## 🔧 性能优化规范

### 1. 组件优化

```typescript
// ✅ 使用 memo 优化重渲染
export const ExpensiveComponent = memo(({ data }: Props) => {
  return <div>{/* 复杂渲染逻辑 */}</div>;
});

// ✅ 使用 useCallback 优化函数引用
const handleClick = useCallback((id: string) => {
  onAction(id);
}, [onAction]);

// ✅ 使用 useMemo 优化计算
const expensiveValue = useMemo(() => {
  return data.reduce((acc, item) => acc + item.value, 0);
}, [data]);
```

### 2. 渲染优化

```typescript
// ✅ 条件渲染优化
{isVisible && <ExpensiveComponent />}

// ✅ 列表渲染优化
{items.map((item) => (
  <MemoizedItem key={item.id} item={item} />
))}

// ✅ 懒加载
const LazyComponent = lazy(() => import('./LazyComponent'));

<Suspense fallback={<Skeleton />}>
  <LazyComponent />
</Suspense>
```

这个组件开发指南为 Augment 提供了详细的组件开发规范和最佳实践，确保代码质量和一致性。
