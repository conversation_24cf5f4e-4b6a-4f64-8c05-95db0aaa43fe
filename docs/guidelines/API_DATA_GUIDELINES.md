# API 和数据处理指南

## 📡 API 设计规范

### 1. 数据类型定义

```typescript
// ✅ 基础数据类型
interface User {
  id: string;
  name: string;
  organization: string;
  email: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

interface Archive {
  id: string;
  title: string;
  description: string;
  category: string;
  status: 'draft' | 'pending' | 'approved' | 'archived';
  fileUrl?: string;
  metadata: {
    size: number;
    format: string;
    uploadDate: string;
  };
  tags: string[];
  creator: Pick<User, 'id' | 'name'>;
}

interface ArchiveFilter {
  type: 'category' | 'status' | 'date' | 'creator';
  value: string | string[];
  operator?: 'eq' | 'in' | 'gte' | 'lte' | 'contains';
}

interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  pagination?: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}
```

### 2. API 请求模式

```typescript
// ✅ 标准 API 服务类
class ArchiveService {
  private baseUrl = '/api/archives';

  async getArchives(
    params: PaginationParams & { filters?: ArchiveFilter[] }
  ): Promise<ApiResponse<Archive[]>> {
    const searchParams = new URLSearchParams({
      page: params.page.toString(),
      pageSize: params.pageSize.toString(),
      ...(params.sortBy && { sortBy: params.sortBy }),
      ...(params.sortOrder && { sortOrder: params.sortOrder }),
    });

    if (params.filters) {
      searchParams.append('filters', JSON.stringify(params.filters));
    }

    const response = await fetch(`${this.baseUrl}?${searchParams}`);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  async getArchiveById(id: string): Promise<ApiResponse<Archive>> {
    const response = await fetch(`${this.baseUrl}/${id}`);
    
    if (!response.ok) {
      throw new Error(`Archive not found: ${id}`);
    }

    return response.json();
  }

  async createArchive(data: Omit<Archive, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Archive>> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to create archive');
    }

    return response.json();
  }

  async updateArchive(id: string, data: Partial<Archive>): Promise<ApiResponse<Archive>> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to update archive');
    }

    return response.json();
  }

  async deleteArchive(id: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete archive');
    }

    return response.json();
  }
}

// 导出单例实例
export const archiveService = new ArchiveService();
```

## 🔄 数据获取 Hooks

### 1. 基础数据获取 Hook

```typescript
import { useState, useEffect } from 'react';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

function useApi<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  dependencies: any[] = []
): UseApiState<T> & { refetch: () => void } {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: true,
    error: null,
  });

  const fetchData = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await apiCall();
      setState({
        data: response.data,
        loading: false,
        error: null,
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  useEffect(() => {
    fetchData();
  }, dependencies);

  return {
    ...state,
    refetch: fetchData,
  };
}

// ✅ 使用示例
function ArchiveList() {
  const { data: archives, loading, error, refetch } = useApi(
    () => archiveService.getArchives({ page: 1, pageSize: 10 }),
    []
  );

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {archives?.map(archive => (
        <div key={archive.id}>{archive.title}</div>
      ))}
      <button onClick={refetch}>Refresh</button>
    </div>
  );
}
```

### 2. 分页数据 Hook

```typescript
interface UsePaginatedDataOptions {
  pageSize?: number;
  initialFilters?: ArchiveFilter[];
}

function usePaginatedArchives(options: UsePaginatedDataOptions = {}) {
  const { pageSize = 10, initialFilters = [] } = options;
  
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<ArchiveFilter[]>(initialFilters);
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const { data, loading, error, refetch } = useApi(
    () => archiveService.getArchives({
      page,
      pageSize,
      sortBy,
      sortOrder,
      filters,
    }),
    [page, pageSize, sortBy, sortOrder, JSON.stringify(filters)]
  );

  const goToPage = (newPage: number) => {
    setPage(newPage);
  };

  const updateFilters = (newFilters: ArchiveFilter[]) => {
    setFilters(newFilters);
    setPage(1); // 重置到第一页
  };

  const updateSort = (field: string, order: 'asc' | 'desc') => {
    setSortBy(field);
    setSortOrder(order);
    setPage(1);
  };

  return {
    archives: data?.data || [],
    pagination: data?.pagination,
    loading,
    error,
    page,
    filters,
    sortBy,
    sortOrder,
    goToPage,
    updateFilters,
    updateSort,
    refetch,
  };
}
```

### 3. 搜索数据 Hook

```typescript
import { useMemo } from 'react';
import { debounce } from 'lodash';

function useArchiveSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Archive[]>([]);
  const [searching, setSearching] = useState(false);

  const debouncedSearch = useMemo(
    () => debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        return;
      }

      try {
        setSearching(true);
        const response = await archiveService.searchArchives(query);
        setSearchResults(response.data);
      } catch (error) {
        console.error('Search error:', error);
        setSearchResults([]);
      } finally {
        setSearching(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    debouncedSearch(searchQuery);
    return () => debouncedSearch.cancel();
  }, [searchQuery, debouncedSearch]);

  return {
    searchQuery,
    setSearchQuery,
    searchResults,
    searching,
  };
}
```

## 💾 数据缓存策略

### 1. 简单内存缓存

```typescript
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }
}

export const cacheManager = new CacheManager();

// ✅ 带缓存的 API 服务
class CachedArchiveService extends ArchiveService {
  async getArchiveById(id: string): Promise<ApiResponse<Archive>> {
    const cacheKey = `archive:${id}`;
    const cached = cacheManager.get<ApiResponse<Archive>>(cacheKey);
    
    if (cached) {
      return cached;
    }

    const response = await super.getArchiveById(id);
    cacheManager.set(cacheKey, response, 5 * 60 * 1000); // 5分钟缓存
    
    return response;
  }
}
```

### 2. 缓存 Hook

```typescript
function useCachedApi<T>(
  cacheKey: string,
  apiCall: () => Promise<ApiResponse<T>>,
  ttl: number = 5 * 60 * 1000
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    const fetchData = async () => {
      // 检查缓存
      const cached = cacheManager.get<T>(cacheKey);
      if (cached) {
        setState({
          data: cached,
          loading: false,
          error: null,
        });
        return;
      }

      // 从 API 获取
      try {
        setState(prev => ({ ...prev, loading: true }));
        const response = await apiCall();
        
        // 缓存数据
        cacheManager.set(cacheKey, response.data, ttl);
        
        setState({
          data: response.data,
          loading: false,
          error: null,
        });
      } catch (error) {
        setState({
          data: null,
          loading: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    };

    fetchData();
  }, [cacheKey]);

  return state;
}
```

## 🔄 数据同步和更新

### 1. 乐观更新

```typescript
function useOptimisticUpdate<T>() {
  const [optimisticData, setOptimisticData] = useState<T | null>(null);

  const performOptimisticUpdate = async <R>(
    currentData: T,
    optimisticUpdate: (data: T) => T,
    apiCall: () => Promise<R>,
    onSuccess?: (result: R) => void,
    onError?: (error: Error) => void
  ) => {
    // 立即应用乐观更新
    const optimistic = optimisticUpdate(currentData);
    setOptimisticData(optimistic);

    try {
      // 执行 API 调用
      const result = await apiCall();
      
      // 成功后清除乐观数据
      setOptimisticData(null);
      onSuccess?.(result);
      
      return result;
    } catch (error) {
      // 失败后回滚
      setOptimisticData(null);
      onError?.(error as Error);
      throw error;
    }
  };

  return {
    optimisticData,
    performOptimisticUpdate,
  };
}

// ✅ 使用示例
function ArchiveItem({ archive }: { archive: Archive }) {
  const { optimisticData, performOptimisticUpdate } = useOptimisticUpdate<Archive>();
  const displayArchive = optimisticData || archive;

  const handleStatusUpdate = async (newStatus: Archive['status']) => {
    await performOptimisticUpdate(
      archive,
      (data) => ({ ...data, status: newStatus }),
      () => archiveService.updateArchive(archive.id, { status: newStatus }),
      () => {
        // 成功回调
        console.log('Status updated successfully');
      },
      (error) => {
        // 错误回调
        console.error('Failed to update status:', error);
      }
    );
  };

  return (
    <div>
      <h3>{displayArchive.title}</h3>
      <p>Status: {displayArchive.status}</p>
      <button onClick={() => handleStatusUpdate('approved')}>
        Approve
      </button>
    </div>
  );
}
```

## 🔍 数据验证

### 1. 输入验证

```typescript
// ✅ 数据验证函数
function validateArchive(data: Partial<Archive>): { isValid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};

  if (!data.title?.trim()) {
    errors.title = '标题不能为空';
  } else if (data.title.length > 100) {
    errors.title = '标题不能超过100个字符';
  }

  if (!data.description?.trim()) {
    errors.description = '描述不能为空';
  } else if (data.description.length > 500) {
    errors.description = '描述不能超过500个字符';
  }

  if (!data.category) {
    errors.category = '请选择分类';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

// ✅ 表单验证 Hook
function useFormValidation<T>(
  initialData: T,
  validator: (data: T) => { isValid: boolean; errors: Record<string, string> }
) {
  const [data, setData] = useState<T>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const updateField = (field: keyof T, value: any) => {
    setData(prev => ({ ...prev, [field]: value }));
    
    // 清除该字段的错误
    if (errors[field as string]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });
    }
  };

  const touchField = (field: keyof T) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  const validate = () => {
    const validation = validator(data);
    setErrors(validation.errors);
    return validation.isValid;
  };

  const reset = () => {
    setData(initialData);
    setErrors({});
    setTouched({});
  };

  return {
    data,
    errors,
    touched,
    updateField,
    touchField,
    validate,
    reset,
    isValid: Object.keys(errors).length === 0,
  };
}
```

## 📊 数据格式化

### 1. 数据格式化工具

```typescript
// ✅ 日期格式化
export const formatDate = (date: string | Date, format: 'short' | 'long' | 'relative' = 'short') => {
  const d = new Date(date);
  
  switch (format) {
    case 'short':
      return d.toLocaleDateString('zh-CN');
    case 'long':
      return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    case 'relative':
      const now = new Date();
      const diff = now.getTime() - d.getTime();
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      
      if (days === 0) return '今天';
      if (days === 1) return '昨天';
      if (days < 7) return `${days}天前`;
      if (days < 30) return `${Math.floor(days / 7)}周前`;
      return d.toLocaleDateString('zh-CN');
    default:
      return d.toLocaleDateString('zh-CN');
  }
};

// ✅ 文件大小格式化
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

// ✅ 状态格式化
export const formatArchiveStatus = (status: Archive['status']): { label: string; color: string } => {
  const statusMap = {
    draft: { label: '草稿', color: 'gray' },
    pending: { label: '待审核', color: 'yellow' },
    approved: { label: '已通过', color: 'green' },
    archived: { label: '已归档', color: 'blue' },
  };
  
  return statusMap[status] || { label: '未知', color: 'gray' };
};
```

这个 API 和数据处理指南为 Augment 提供了完整的数据层开发规范，确保数据处理的一致性和可靠性。
