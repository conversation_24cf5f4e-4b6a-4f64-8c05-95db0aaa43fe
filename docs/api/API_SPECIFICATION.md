# 教育厅档案管理系统 API 接口规范

## 📋 概述

本文档定义了教育厅档案管理系统的完整 API 接口规范，包括请求地址、参数、响应格式和示例数据。

## 🔧 通用规范

### 基础信息
- **Base URL**: `https://api.archive.edu.cn/v1`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

### 认证方式
```http
Authorization: Bearer <JWT_TOKEN>
```

### 通用响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

interface PaginatedResponse<T> {
  success: boolean;
  code: number;
  message: string;
  data: {
    items: T[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  };
  timestamp: number;
}
```

### 状态码规范
- `200` - 成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## 🔐 认证接口

### 1. 用户登录
```http
POST /auth/login
```

**请求参数**:
```typescript
interface LoginRequest {
  username: string;
  password: string;
  captcha?: string;
}
```

**响应数据**:
```typescript
interface LoginResponse {
  token: string;
  refreshToken: string;
  user: {
    id: number;
    username: string;
    realname: string;
    email: string;
    dept: {
      id: number;
      name: string;
    };
    roles: string[];
    permissions: string[];
  };
  expiresIn: number;
}
```

**示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "user": {
      "id": 1,
      "username": "admin",
      "realname": "管理员",
      "email": "<EMAIL>",
      "dept": {
        "id": 1,
        "name": "办公室"
      },
      "roles": ["admin"],
      "permissions": ["archive:read", "archive:write", "user:manage"]
    },
    "expiresIn": 7200
  },
  "timestamp": 1703123456789
}
```

### 2. 刷新令牌
```http
POST /auth/refresh
```

**请求参数**:
```typescript
interface RefreshTokenRequest {
  refreshToken: string;
}
```

### 3. 用户登出
```http
POST /auth/logout
```

## 📁 档案管理接口

### 1. 获取档案列表
```http
GET /archives
```

**查询参数**:
```typescript
interface ArchiveListQuery {
  page?: number;           // 页码，默认1
  pageSize?: number;       // 每页数量，默认10
  status?: string;         // 状态筛选
  year?: number;          // 年份筛选
  deptId?: number;        // 部门筛选
  keyword?: string;       // 关键词搜索
  startDate?: string;     // 开始日期 YYYY-MM-DD
  endDate?: string;       // 结束日期 YYYY-MM-DD
  archiveType?: string;   // 档案类型
  sortBy?: string;        // 排序字段
  sortOrder?: 'asc' | 'desc'; // 排序方向
}
```

**响应数据**:
```typescript
interface ArchiveItem {
  id: number;
  archiveNo: string;
  title: string;
  type: string;
  status: 'pending' | 'to-archive' | 'not-archive' | 'temp-not-archive' | 'archived';
  year: number;
  dept: {
    id: number;
    name: string;
  };
  responsiblePerson: string;
  formTime: string;
  retentionPeriod: string;
  securityLevel: string;
  pageCount: number;
  createdAt: string;
  updatedAt: string;
}
```

**示例响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1,
        "archiveNo": "ZJS-2025-0001",
        "title": "浙江省教育厅关于省政协十二届五次会议第604号提案的答复",
        "type": "发文",
        "status": "pending",
        "year": 2025,
        "dept": {
          "id": 1,
          "name": "办公室"
        },
        "responsiblePerson": "浙江省教育厅",
        "formTime": "2025-06-23",
        "retentionPeriod": "30年",
        "securityLevel": "公开",
        "pageCount": 5,
        "createdAt": "2025-06-23T09:48:00Z",
        "updatedAt": "2025-06-23T09:48:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 156,
      "totalPages": 16
    }
  },
  "timestamp": 1703123456789
}
```

### 2. 获取档案详情
```http
GET /archives/{id}
```

**路径参数**:
- `id` - 档案ID

**响应数据**:
```typescript
interface ArchiveDetail extends ArchiveItem {
  fondsNo: string;
  directoryId: number;
  serialNo: number;
  docNo: string;
  keywords: string[];
  subjectTerms: string[];
  personInvolved: string[];
  note: string;
  files: ArchiveFile[];
  metadata: {
    fileTitle: string;
    directoryNo: string;
    classificationNo: string;
    carrierType: string;
    boxNo: string;
    fulltextRecognition: string;
  };
}

interface ArchiveFile {
  id: number;
  fileName: string;
  originalName: string;
  fileSize: number;
  fileType: string;
  category: 'main' | 'approval' | 'attachment' | 'related';
  downloadUrl: string;
  createdAt: string;
}
```

### 3. 创建档案
```http
POST /archives
```

**请求参数**:
```typescript
interface CreateArchiveRequest {
  title: string;
  type: string;
  year: number;
  deptId: number;
  directoryId: number;
  responsiblePerson: string;
  formTime: string;
  retentionPeriod: string;
  securityLevel: string;
  docNo?: string;
  keywords?: string[];
  note?: string;
  metadata?: {
    fileTitle?: string;
    directoryNo?: string;
    classificationNo?: string;
    carrierType?: string;
    boxNo?: string;
  };
}
```

### 4. 更新档案
```http
PUT /archives/{id}
```

### 5. 批量更新档案状态
```http
PATCH /archives/batch-status
```

**请求参数**:
```typescript
interface BatchUpdateStatusRequest {
  ids: number[];
  status: 'pending' | 'to-archive' | 'not-archive' | 'temp-not-archive' | 'archived';
  remark?: string;
}
```

### 6. 删除档案
```http
DELETE /archives/{id}
```

## 📂 文件管理接口

### 1. 上传文件
```http
POST /files/upload
```

**请求参数** (multipart/form-data):
```typescript
interface FileUploadRequest {
  file: File;
  archiveId: number;
  category: 'main' | 'approval' | 'attachment' | 'related';
  subtype?: string;
}
```

**响应数据**:
```typescript
interface FileUploadResponse {
  id: number;
  fileName: string;
  originalName: string;
  fileSize: number;
  fileType: string;
  downloadUrl: string;
  uploadProgress: number;
}
```

### 2. 下载文件
```http
GET /files/{id}/download
```

### 3. 删除文件
```http
DELETE /files/{id}
```

## 🔍 搜索接口

### 1. 简单搜索
```http
GET /search/simple
```

**查询参数**:
```typescript
interface SimpleSearchQuery {
  keyword: string;
  archiveType?: string;
  year?: number;
  page?: number;
  pageSize?: number;
}
```

### 2. 高级搜索
```http
POST /search/advanced
```

**请求参数**:
```typescript
interface AdvancedSearchRequest {
  title?: string;
  docNo?: string;
  responsiblePerson?: string;
  keywords?: string[];
  archiveType?: string;
  securityLevel?: string;
  retentionPeriod?: string;
  startDate?: string;
  endDate?: string;
  deptIds?: number[];
  page?: number;
  pageSize?: number;
}
```

### 3. 全文搜索
```http
POST /search/fulltext
```

**请求参数**:
```typescript
interface FulltextSearchRequest {
  query: string;
  filters?: {
    archiveType?: string;
    year?: number;
    deptId?: number;
  };
  highlight?: boolean;
  page?: number;
  pageSize?: number;
}
```

**响应数据**:
```typescript
interface SearchResult {
  id: number;
  title: string;
  archiveNo: string;
  summary: string;
  highlights?: string[];
  score: number;
  matchedFields: string[];
}
```

## 📋 借阅管理接口

### 1. 获取借阅记录
```http
GET /borrowing/records
```

**查询参数**:
```typescript
interface BorrowingRecordsQuery {
  status?: 'applying' | 'borrowed' | 'returned';
  borrower?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}
```

**响应数据**:
```typescript
interface BorrowingRecord {
  id: number;
  archive: {
    id: number;
    title: string;
    archiveNo: string;
    type: string;
  };
  borrower: {
    id: number;
    name: string;
    unit: string;
  };
  purpose: string;
  borrowTime: string;
  expectedReturnTime: string;
  actualReturnTime?: string;
  status: 'applying' | 'borrowed' | 'returned';
  approvalStatus: 'pending' | 'approved' | 'rejected';
  downloadCount: number;
  registrar: string;
}
```

### 2. 创建借阅申请
```http
POST /borrowing/applications
```

**请求参数**:
```typescript
interface CreateBorrowingRequest {
  archiveId: number;
  purpose: string;
  expectedStartTime: string;
  expectedEndTime: string;
  remark?: string;
}
```

### 3. 审批借阅申请
```http
PATCH /borrowing/applications/{id}/approve
```

**请求参数**:
```typescript
interface ApproveBorrowingRequest {
  status: 'approved' | 'rejected';
  opinion: string;
  conditions?: string[];
}
```

### 4. 归还档案
```http
PATCH /borrowing/records/{id}/return
```

**请求参数**:
```typescript
interface ReturnArchiveRequest {
  returnTime: string;
  condition: string;
  remark?: string;
}
```

## 📊 统计分析接口

### 1. 获取档案统计数据
```http
GET /statistics/archives
```

**查询参数**:
```typescript
interface ArchiveStatisticsQuery {
  year?: number;
  deptId?: number;
  timeRange?: 'month' | 'quarter' | 'year';
}
```

**响应数据**:
```typescript
interface ArchiveStatistics {
  total: number;
  byStatus: {
    pending: number;
    archived: number;
    notArchive: number;
    tempNotArchive: number;
  };
  byType: {
    type: string;
    count: number;
  }[];
  byDepartment: {
    deptId: number;
    deptName: string;
    count: number;
  }[];
  monthlyTrend: {
    month: string;
    archived: number;
    pending: number;
  }[];
}
```

### 2. 获取借阅统计数据
```http
GET /statistics/borrowing
```

### 3. 获取系统使用统计
```http
GET /statistics/usage
```

## 🏢 组织机构接口

### 1. 获取部门列表
```http
GET /departments
```

**响应数据**:
```typescript
interface Department {
  id: number;
  name: string;
  code: string;
  parentId?: number;
  children?: Department[];
  level: number;
  sortOrder: number;
  status: 'active' | 'inactive';
}
```

### 2. 获取部门树形结构
```http
GET /departments/tree
```

## 📁 目录管理接口

### 1. 获取档案目录
```http
GET /directories
```

**查询参数**:
```typescript
interface DirectoryQuery {
  parentId?: number;
  level?: number;
  status?: 'active' | 'inactive';
}
```

**响应数据**:
```typescript
interface Directory {
  id: number;
  name: string;
  code: string;
  parentId?: number;
  level: number;
  sortOrder: number;
  status: 'active' | 'inactive';
  children?: Directory[];
}
```

### 2. 获取目录树形结构
```http
GET /directories/tree
```

## 👥 用户管理接口

### 1. 获取用户列表
```http
GET /users
```

**查询参数**:
```typescript
interface UserListQuery {
  deptId?: number;
  status?: 'active' | 'inactive' | 'locked';
  keyword?: string;
  page?: number;
  pageSize?: number;
}
```

**响应数据**:
```typescript
interface User {
  id: number;
  username: string;
  realname: string;
  email: string;
  phone: string;
  dept: {
    id: number;
    name: string;
  };
  roles: {
    id: number;
    name: string;
    code: string;
  }[];
  status: 'active' | 'inactive' | 'locked';
  lastLoginAt?: string;
  createdAt: string;
}
```

### 2. 创建用户
```http
POST /users
```

### 3. 更新用户
```http
PUT /users/{id}
```

### 4. 删除用户
```http
DELETE /users/{id}
```

## 🔑 权限管理接口

### 1. 获取角色列表
```http
GET /roles
```

### 2. 获取权限列表
```http
GET /permissions
```

### 3. 分配用户角色
```http
POST /users/{userId}/roles
```

**请求参数**:
```typescript
interface AssignRolesRequest {
  roleIds: number[];
}
```

## ⚙️ 系统配置接口

### 1. 获取系统配置
```http
GET /system/configs
```

**查询参数**:
```typescript
interface ConfigQuery {
  group?: string;
  key?: string;
}
```

**响应数据**:
```typescript
interface SystemConfig {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json';
  group: string;
  description: string;
}
```

### 2. 更新系统配置
```http
PUT /system/configs/{key}
```

## 📝 操作日志接口

### 1. 获取操作日志
```http
GET /logs/operations
```

**查询参数**:
```typescript
interface OperationLogQuery {
  userId?: number;
  archiveId?: number;
  operationType?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}
```

**响应数据**:
```typescript
interface OperationLog {
  id: number;
  archive: {
    id: number;
    title: string;
    archiveNo: string;
  };
  operator: {
    id: number;
    name: string;
  };
  operationType: string;
  operationDetails: string;
  operationTime: string;
  ipAddress: string;
  clientType: string;
}
```

## 🔌 第三方系统接口

### 1. 档案数据上传
```http
POST /api/external/archives/upload
```

**请求头**:
```http
X-App-Id: your_app_id
X-Timestamp: 1703123456
X-Signature: calculated_signature
```

**请求参数**:
```typescript
interface ExternalArchiveUpload {
  sourceId: string;
  title: string;
  type: string;
  year: number;
  deptCode: string;
  responsiblePerson: string;
  formTime: string;
  retentionPeriod: string;
  securityLevel: string;
  files: {
    fileName: string;
    fileContent: string; // base64编码
    fileType: string;
    category: string;
  }[];
  metadata?: Record<string, any>;
}
```

### 2. 批量档案上传
```http
POST /api/external/archives/batch-upload
```

### 3. 查询上传状态
```http
GET /api/external/archives/upload-status/{batchId}
```

## 📋 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 参数验证失败 | 检查请求参数格式和必填项 |
| 1002 | 认证失败 | 检查 token 是否有效 |
| 1003 | 权限不足 | 联系管理员分配相应权限 |
| 2001 | 档案不存在 | 检查档案ID是否正确 |
| 2002 | 档案状态不允许操作 | 检查档案当前状态 |
| 3001 | 文件上传失败 | 检查文件格式和大小限制 |
| 3002 | 文件不存在 | 检查文件ID是否正确 |
| 4001 | 用户不存在 | 检查用户ID是否正确 |
| 4002 | 用户已被锁定 | 联系管理员解锁用户 |
| 5001 | 系统配置错误 | 联系系统管理员 |
| 9999 | 系统内部错误 | 联系技术支持 |
