# API 接口测试指南

## 📋 概述

本文档提供了教育厅档案管理系统 API 接口的测试指南，包括测试环境配置、测试用例和自动化测试脚本。

## 🔧 测试环境配置

### 环境信息
- **开发环境**: `https://dev-api.archive.edu.cn/v1`
- **测试环境**: `https://test-api.archive.edu.cn/v1`
- **生产环境**: `https://api.archive.edu.cn/v1`

### 测试账号
```json
{
  "admin": {
    "username": "test_admin",
    "password": "Test123456!",
    "permissions": ["all"]
  },
  "manager": {
    "username": "test_manager",
    "password": "Test123456!",
    "permissions": ["archive:read", "archive:write", "user:read"]
  },
  "user": {
    "username": "test_user",
    "password": "Test123456!",
    "permissions": ["archive:read"]
  }
}
```

## 🧪 Postman 测试集合

### 环境变量配置
```json
{
  "baseUrl": "https://test-api.archive.edu.cn/v1",
  "token": "",
  "userId": "",
  "archiveId": "",
  "fileId": ""
}
```

### 预请求脚本 (Pre-request Script)
```javascript
// 自动获取 token
if (!pm.environment.get("token")) {
    pm.sendRequest({
        url: pm.environment.get("baseUrl") + "/auth/login",
        method: 'POST',
        header: {
            'Content-Type': 'application/json'
        },
        body: {
            mode: 'raw',
            raw: JSON.stringify({
                username: "test_admin",
                password: "Test123456!"
            })
        }
    }, function (err, response) {
        if (response.json().success) {
            pm.environment.set("token", response.json().data.token);
            pm.environment.set("userId", response.json().data.user.id);
        }
    });
}
```

### 测试脚本 (Tests)
```javascript
// 通用响应验证
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has success field", function () {
    pm.expect(pm.response.json()).to.have.property('success');
});

pm.test("Response has data field", function () {
    pm.expect(pm.response.json()).to.have.property('data');
});

pm.test("Response time is less than 2000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});

// 保存响应数据到环境变量
if (pm.response.json().success && pm.response.json().data) {
    const data = pm.response.json().data;
    
    // 保存档案ID
    if (data.id) {
        pm.environment.set("archiveId", data.id);
    }
    
    // 保存文件ID
    if (data.files && data.files.length > 0) {
        pm.environment.set("fileId", data.files[0].id);
    }
}
```

## 🔍 核心接口测试用例

### 1. 认证接口测试

#### 登录成功测试
```http
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "test_admin",
  "password": "Test123456!"
}
```

**期望结果**:
- 状态码: 200
- 响应包含 token 和用户信息
- token 格式正确

#### 登录失败测试
```http
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "test_admin",
  "password": "wrong_password"
}
```

**期望结果**:
- 状态码: 200
- success: false
- 错误码: 1002

### 2. 档案管理接口测试

#### 获取档案列表测试
```http
GET {{baseUrl}}/archives?page=1&pageSize=10&status=pending
Authorization: Bearer {{token}}
```

**测试脚本**:
```javascript
pm.test("Archives list structure is correct", function () {
    const response = pm.response.json();
    pm.expect(response.data).to.have.property('items');
    pm.expect(response.data).to.have.property('pagination');
    pm.expect(response.data.items).to.be.an('array');
});

pm.test("Pagination info is correct", function () {
    const pagination = pm.response.json().data.pagination;
    pm.expect(pagination).to.have.property('page');
    pm.expect(pagination).to.have.property('pageSize');
    pm.expect(pagination).to.have.property('total');
    pm.expect(pagination).to.have.property('totalPages');
});
```

#### 创建档案测试
```http
POST {{baseUrl}}/archives
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "测试档案标题",
  "type": "测试类型",
  "year": 2025,
  "deptId": 1,
  "directoryId": 101,
  "responsiblePerson": "测试负责人",
  "formTime": "2025-06-25",
  "retentionPeriod": "10年",
  "securityLevel": "公开",
  "docNo": "TEST-2025-001",
  "keywords": ["测试", "档案"],
  "note": "这是一个测试档案"
}
```

**测试脚本**:
```javascript
pm.test("Archive created successfully", function () {
    const response = pm.response.json();
    pm.expect(response.success).to.be.true;
    pm.expect(response.data).to.have.property('id');
    pm.expect(response.data.title).to.eql("测试档案标题");
    
    // 保存创建的档案ID用于后续测试
    pm.environment.set("testArchiveId", response.data.id);
});
```

#### 更新档案测试
```http
PUT {{baseUrl}}/archives/{{testArchiveId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "更新后的档案标题",
  "note": "档案已更新"
}
```

#### 删除档案测试
```http
DELETE {{baseUrl}}/archives/{{testArchiveId}}
Authorization: Bearer {{token}}
```

### 3. 文件上传测试

#### 文件上传测试
```http
POST {{baseUrl}}/files/upload
Authorization: Bearer {{token}}
Content-Type: multipart/form-data

file: [选择文件]
archiveId: {{archiveId}}
category: main
```

**测试脚本**:
```javascript
pm.test("File uploaded successfully", function () {
    const response = pm.response.json();
    pm.expect(response.success).to.be.true;
    pm.expect(response.data).to.have.property('id');
    pm.expect(response.data).to.have.property('fileName');
    pm.expect(response.data).to.have.property('downloadUrl');
});
```

### 4. 搜索接口测试

#### 简单搜索测试
```http
GET {{baseUrl}}/search/simple?keyword=教师&page=1&pageSize=5
Authorization: Bearer {{token}}
```

#### 全文搜索测试
```http
POST {{baseUrl}}/search/fulltext
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "query": "教师培训",
  "highlight": true,
  "page": 1,
  "pageSize": 10
}
```

**测试脚本**:
```javascript
pm.test("Search results have highlights", function () {
    const response = pm.response.json();
    if (response.data.items.length > 0) {
        pm.expect(response.data.items[0]).to.have.property('highlights');
        pm.expect(response.data.items[0].highlights).to.be.an('array');
    }
});
```

## 🚀 自动化测试脚本

### Newman 命令行测试
```bash
# 安装 Newman
npm install -g newman

# 运行测试集合
newman run archive-api-tests.postman_collection.json \
  -e test-environment.postman_environment.json \
  --reporters cli,html \
  --reporter-html-export test-results.html
```

### Jest 单元测试示例
```javascript
const axios = require('axios');

describe('Archive API Tests', () => {
  let token;
  let baseURL = 'https://test-api.archive.edu.cn/v1';
  
  beforeAll(async () => {
    // 获取认证 token
    const response = await axios.post(`${baseURL}/auth/login`, {
      username: 'test_admin',
      password: 'Test123456!'
    });
    token = response.data.data.token;
  });

  test('should get archives list', async () => {
    const response = await axios.get(`${baseURL}/archives`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    expect(response.status).toBe(200);
    expect(response.data.success).toBe(true);
    expect(response.data.data).toHaveProperty('items');
    expect(response.data.data).toHaveProperty('pagination');
  });

  test('should create archive', async () => {
    const archiveData = {
      title: '测试档案',
      type: '测试',
      year: 2025,
      deptId: 1,
      directoryId: 101,
      responsiblePerson: '测试人员',
      formTime: '2025-06-25',
      retentionPeriod: '10年',
      securityLevel: '公开'
    };

    const response = await axios.post(`${baseURL}/archives`, archiveData, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    expect(response.status).toBe(200);
    expect(response.data.success).toBe(true);
    expect(response.data.data).toHaveProperty('id');
  });
});
```

### Python 自动化测试脚本
```python
import requests
import pytest

class TestArchiveAPI:
    base_url = "https://test-api.archive.edu.cn/v1"
    token = None
    
    @classmethod
    def setup_class(cls):
        """获取认证 token"""
        response = requests.post(f"{cls.base_url}/auth/login", json={
            "username": "test_admin",
            "password": "Test123456!"
        })
        cls.token = response.json()["data"]["token"]
    
    def test_get_archives_list(self):
        """测试获取档案列表"""
        headers = {"Authorization": f"Bearer {self.token}"}
        response = requests.get(f"{self.base_url}/archives", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "items" in data["data"]
        assert "pagination" in data["data"]
    
    def test_create_archive(self):
        """测试创建档案"""
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        archive_data = {
            "title": "Python测试档案",
            "type": "测试",
            "year": 2025,
            "deptId": 1,
            "directoryId": 101,
            "responsiblePerson": "Python测试",
            "formTime": "2025-06-25",
            "retentionPeriod": "10年",
            "securityLevel": "公开"
        }
        
        response = requests.post(
            f"{self.base_url}/archives", 
            json=archive_data, 
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "id" in data["data"]
```

## 📊 性能测试

### Apache Bench (ab) 测试
```bash
# 登录接口性能测试
ab -n 1000 -c 10 -p login.json -T application/json \
  https://test-api.archive.edu.cn/v1/auth/login

# 档案列表接口性能测试
ab -n 1000 -c 10 -H "Authorization: Bearer YOUR_TOKEN" \
  https://test-api.archive.edu.cn/v1/archives
```

### JMeter 测试计划
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Archive API Performance Test">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="API Load Test">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">100</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">50</stringProp>
        <stringProp name="ThreadGroup.ramp_time">10</stringProp>
      </ThreadGroup>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

## 📋 测试检查清单

### 功能测试
- [ ] 用户认证和授权
- [ ] 档案 CRUD 操作
- [ ] 文件上传下载
- [ ] 搜索功能
- [ ] 借阅管理
- [ ] 统计分析
- [ ] 权限控制

### 安全测试
- [ ] SQL 注入防护
- [ ] XSS 防护
- [ ] CSRF 防护
- [ ] 认证绕过测试
- [ ] 权限提升测试
- [ ] 敏感信息泄露测试

### 性能测试
- [ ] 响应时间 < 2秒
- [ ] 并发用户支持 > 100
- [ ] 文件上传性能
- [ ] 数据库查询优化
- [ ] 缓存机制验证

### 兼容性测试
- [ ] 不同浏览器兼容性
- [ ] 移动端适配
- [ ] API 版本兼容性
- [ ] 数据格式兼容性

## 🐛 常见问题排查

### 认证问题
```bash
# 检查 token 是否有效
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://test-api.archive.edu.cn/v1/auth/verify

# 刷新 token
curl -X POST -H "Content-Type: application/json" \
  -d '{"refreshToken":"YOUR_REFRESH_TOKEN"}' \
  https://test-api.archive.edu.cn/v1/auth/refresh
```

### 权限问题
```bash
# 检查用户权限
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://test-api.archive.edu.cn/v1/users/me/permissions
```

### 文件上传问题
```bash
# 检查文件大小限制
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://test-api.archive.edu.cn/v1/system/configs?group=archive

# 测试文件上传
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.pdf" \
  -F "archiveId=123" \
  -F "category=main" \
  https://test-api.archive.edu.cn/v1/files/upload
```

## 📈 测试报告模板

### 测试执行报告
```markdown
# API 测试执行报告

## 测试概要
- 测试时间: 2025-06-25
- 测试环境: 测试环境
- 测试人员: 测试团队
- 测试版本: v1.0.0

## 测试结果
- 总用例数: 156
- 通过用例: 152
- 失败用例: 4
- 通过率: 97.4%

## 失败用例分析
1. 文件上传大小限制测试 - 配置问题
2. 权限边界测试 - 逻辑缺陷
3. 并发访问测试 - 性能问题
4. 数据一致性测试 - 事务问题

## 建议
1. 修复文件上传配置
2. 完善权限控制逻辑
3. 优化数据库查询性能
4. 加强事务管理
```
