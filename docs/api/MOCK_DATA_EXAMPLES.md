# API 模拟数据示例

## 📋 概述

本文档提供了教育厅档案管理系统各个接口的详细模拟数据示例，包含多种业务场景和数据格式。

## 🔐 认证相关模拟数据

### 登录成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6IueuoeeQhuWRmCIsImlhdCI6MTcwMzEyMzQ1NiwiZXhwIjoxNzAzMTMwNjU2fQ.signature",
    "refreshToken": "rt_1703123456_admin_abc123",
    "user": {
      "id": 1,
      "username": "admin",
      "realname": "管理员",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "dept": {
        "id": 1,
        "name": "办公室",
        "code": "BGS"
      },
      "roles": ["admin", "archive_manager"],
      "permissions": [
        "archive:read", "archive:write", "archive:delete",
        "user:manage", "system:config", "statistics:view"
      ]
    },
    "expiresIn": 7200
  },
  "timestamp": 1703123456789
}
```

### 登录失败响应
```json
{
  "success": false,
  "code": 1002,
  "message": "用户名或密码错误",
  "data": null,
  "timestamp": 1703123456789
}
```

## 📁 档案管理模拟数据

### 档案列表响应 - 多种状态
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1,
        "archiveNo": "ZJS-2025-BGS-0001",
        "title": "浙江省教育厅关于省政协十二届五次会议第604号提案的答复",
        "type": "发文",
        "status": "pending",
        "year": 2025,
        "dept": {
          "id": 1,
          "name": "办公室"
        },
        "responsiblePerson": "浙江省教育厅",
        "formTime": "2025-06-23",
        "retentionPeriod": "30年",
        "securityLevel": "公开",
        "pageCount": 5,
        "createdAt": "2025-06-23T09:48:00Z",
        "updatedAt": "2025-06-23T09:48:00Z"
      },
      {
        "id": 2,
        "archiveNo": "ZJS-2025-JCS-0002",
        "title": "关于开展2025年度教育督导工作的通知",
        "type": "通知",
        "status": "to-archive",
        "year": 2025,
        "dept": {
          "id": 2,
          "name": "教育督导处"
        },
        "responsiblePerson": "教育督导处",
        "formTime": "2025-06-20",
        "retentionPeriod": "10年",
        "securityLevel": "公开",
        "pageCount": 3,
        "createdAt": "2025-06-20T14:30:00Z",
        "updatedAt": "2025-06-22T16:45:00Z"
      },
      {
        "id": 3,
        "archiveNo": "ZJS-2025-RSC-0003",
        "title": "2025年度教师职称评审工作方案",
        "type": "方案",
        "status": "archived",
        "year": 2025,
        "dept": {
          "id": 3,
          "name": "人事处"
        },
        "responsiblePerson": "人事处",
        "formTime": "2025-06-15",
        "retentionPeriod": "永久",
        "securityLevel": "内部",
        "pageCount": 12,
        "createdAt": "2025-06-15T10:20:00Z",
        "updatedAt": "2025-06-18T11:30:00Z"
      },
      {
        "id": 4,
        "archiveNo": "ZJS-2025-CWC-0004",
        "title": "2024年度教育经费使用情况报告",
        "type": "报告",
        "status": "not-archive",
        "year": 2025,
        "dept": {
          "id": 4,
          "name": "财务处"
        },
        "responsiblePerson": "财务处",
        "formTime": "2025-06-10",
        "retentionPeriod": "30年",
        "securityLevel": "机密",
        "pageCount": 25,
        "createdAt": "2025-06-10T08:15:00Z",
        "updatedAt": "2025-06-12T09:20:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 156,
      "totalPages": 16
    }
  },
  "timestamp": 1703123456789
}
```

### 档案详情响应 - 完整信息
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "archiveNo": "ZJS-2025-BGS-0001",
    "fondsNo": "ZJS",
    "title": "浙江省教育厅关于省政协十二届五次会议第604号提案的答复",
    "type": "发文",
    "status": "pending",
    "year": 2025,
    "dept": {
      "id": 1,
      "name": "办公室"
    },
    "directoryId": 101,
    "serialNo": 1,
    "docNo": "浙教办函〔2025〕15号",
    "responsiblePerson": "浙江省教育厅",
    "formTime": "2025-06-23",
    "retentionPeriod": "30年",
    "securityLevel": "公开",
    "pageCount": 5,
    "keywords": ["政协提案", "答复", "教育政策"],
    "subjectTerms": ["提案答复", "教育管理"],
    "personInvolved": ["张三", "李四"],
    "note": "此文件为对省政协提案的正式答复，需要及时归档保存。",
    "files": [
      {
        "id": 101,
        "fileName": "提案答复_正文.pdf",
        "originalName": "浙教办函〔2025〕15号.pdf",
        "fileSize": 2048576,
        "fileType": "pdf",
        "category": "main",
        "downloadUrl": "/api/files/101/download",
        "createdAt": "2025-06-23T09:48:00Z"
      },
      {
        "id": 102,
        "fileName": "审签单.pdf",
        "originalName": "审签单_20250623.pdf",
        "fileSize": 512000,
        "fileType": "pdf",
        "category": "approval",
        "downloadUrl": "/api/files/102/download",
        "createdAt": "2025-06-23T09:50:00Z"
      },
      {
        "id": 103,
        "fileName": "相关材料.docx",
        "originalName": "背景材料汇总.docx",
        "fileSize": 1024000,
        "fileType": "docx",
        "category": "related",
        "downloadUrl": "/api/files/103/download",
        "createdAt": "2025-06-23T09:52:00Z"
      }
    ],
    "metadata": {
      "fileTitle": "浙江省教育厅关于省政协十二届五次会议第604号提案的答复",
      "directoryNo": "2025-BGS-001",
      "classificationNo": "D630.1",
      "carrierType": "电子文件",
      "boxNo": "2025-BGS-001",
      "fulltextRecognition": "已识别"
    },
    "createdAt": "2025-06-23T09:48:00Z",
    "updatedAt": "2025-06-23T09:48:00Z"
  },
  "timestamp": 1703123456789
}
```

### 创建档案请求示例
```json
{
  "title": "关于加强中小学生心理健康教育的指导意见",
  "type": "指导意见",
  "year": 2025,
  "deptId": 5,
  "directoryId": 105,
  "responsiblePerson": "基础教育处",
  "formTime": "2025-06-25",
  "retentionPeriod": "永久",
  "securityLevel": "公开",
  "docNo": "浙教基〔2025〕8号",
  "keywords": ["心理健康", "中小学生", "教育指导"],
  "note": "重要政策文件，需要永久保存",
  "metadata": {
    "fileTitle": "关于加强中小学生心理健康教育的指导意见",
    "directoryNo": "2025-JJC-008",
    "classificationNo": "G630.2",
    "carrierType": "电子文件",
    "boxNo": "2025-JJC-002"
  }
}
```

## 🔍 搜索相关模拟数据

### 简单搜索响应
```json
{
  "success": true,
  "code": 200,
  "message": "搜索成功",
  "data": {
    "items": [
      {
        "id": 15,
        "archiveNo": "ZJS-2025-JJC-0015",
        "title": "关于开展中小学教师培训工作的通知",
        "type": "通知",
        "status": "archived",
        "year": 2025,
        "dept": {
          "id": 5,
          "name": "基础教育处"
        },
        "responsiblePerson": "基础教育处",
        "formTime": "2025-05-20",
        "retentionPeriod": "10年",
        "securityLevel": "公开",
        "pageCount": 4,
        "createdAt": "2025-05-20T14:30:00Z",
        "updatedAt": "2025-05-22T16:45:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 3,
      "totalPages": 1
    }
  },
  "timestamp": 1703123456789
}
```

### 全文搜索响应
```json
{
  "success": true,
  "code": 200,
  "message": "搜索成功",
  "data": {
    "items": [
      {
        "id": 15,
        "title": "关于开展中小学教师培训工作的通知",
        "archiveNo": "ZJS-2025-JJC-0015",
        "summary": "为提高中小学教师专业素养，决定开展2025年度教师培训工作...",
        "highlights": [
          "为提高<em>中小学教师</em>专业素养",
          "开展2025年度<em>教师培训</em>工作"
        ],
        "score": 0.95,
        "matchedFields": ["title", "content"]
      },
      {
        "id": 28,
        "title": "中小学教师职业道德规范实施细则",
        "archiveNo": "ZJS-2025-RSC-0028",
        "summary": "根据教育部相关规定，制定本省中小学教师职业道德规范实施细则...",
        "highlights": [
          "制定本省<em>中小学教师</em>职业道德规范",
          "<em>教师培训</em>和考核要求"
        ],
        "score": 0.87,
        "matchedFields": ["title", "content", "keywords"]
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 2,
      "totalPages": 1
    }
  },
  "timestamp": 1703123456789
}
```

## 📋 借阅管理模拟数据

### 借阅记录列表
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1,
        "archive": {
          "id": 15,
          "title": "关于开展中小学教师培训工作的通知",
          "archiveNo": "ZJS-2025-JJC-0015",
          "type": "通知"
        },
        "borrower": {
          "id": 5,
          "name": "王五",
          "unit": "基础教育处"
        },
        "purpose": "工作参考，制定本处室培训计划",
        "borrowTime": "2025-06-20T09:00:00Z",
        "expectedReturnTime": "2025-06-27T17:00:00Z",
        "actualReturnTime": null,
        "status": "borrowed",
        "approvalStatus": "approved",
        "downloadCount": 3,
        "registrar": "档案管理员"
      },
      {
        "id": 2,
        "archive": {
          "id": 28,
          "title": "中小学教师职业道德规范实施细则",
          "archiveNo": "ZJS-2025-RSC-0028",
          "type": "实施细则"
        },
        "borrower": {
          "id": 8,
          "name": "赵六",
          "unit": "人事处"
        },
        "purpose": "审计检查需要",
        "borrowTime": "2025-06-18T14:30:00Z",
        "expectedReturnTime": "2025-06-25T17:00:00Z",
        "actualReturnTime": "2025-06-24T16:30:00Z",
        "status": "returned",
        "approvalStatus": "approved",
        "downloadCount": 1,
        "registrar": "档案管理员"
      },
      {
        "id": 3,
        "archive": {
          "id": 42,
          "title": "2024年度教育经费预算执行情况报告",
          "archiveNo": "ZJS-2024-CWC-0042",
          "type": "报告"
        },
        "borrower": {
          "id": 12,
          "name": "孙七",
          "unit": "审计处"
        },
        "purpose": "年度审计工作需要",
        "borrowTime": null,
        "expectedReturnTime": "2025-07-01T17:00:00Z",
        "actualReturnTime": null,
        "status": "applying",
        "approvalStatus": "pending",
        "downloadCount": 0,
        "registrar": null
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 25,
      "totalPages": 3
    }
  },
  "timestamp": 1703123456789
}
```

### 创建借阅申请请求
```json
{
  "archiveId": 35,
  "purpose": "制定新学期教学计划，需要参考相关政策文件",
  "expectedStartTime": "2025-06-26T09:00:00Z",
  "expectedEndTime": "2025-07-03T17:00:00Z",
  "remark": "紧急需要，请优先处理"
}
```

### 借阅申请审批请求
```json
{
  "status": "approved",
  "opinion": "同意借阅，请按时归还",
  "conditions": [
    "仅限本人使用",
    "不得复制传播",
    "按时归还"
  ]
}
```

## 📊 统计分析模拟数据

### 档案统计数据
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 1256,
    "byStatus": {
      "pending": 45,
      "archived": 1089,
      "notArchive": 78,
      "tempNotArchive": 44
    },
    "byType": [
      { "type": "发文", "count": 456 },
      { "type": "通知", "count": 234 },
      { "type": "报告", "count": 189 },
      { "type": "方案", "count": 156 },
      { "type": "制度", "count": 98 },
      { "type": "其他", "count": 123 }
    ],
    "byDepartment": [
      { "deptId": 1, "deptName": "办公室", "count": 234 },
      { "deptId": 2, "deptName": "教育督导处", "count": 189 },
      { "deptId": 3, "deptName": "人事处", "count": 167 },
      { "deptId": 4, "deptName": "财务处", "count": 145 },
      { "deptId": 5, "deptName": "基础教育处", "count": 198 },
      { "deptId": 6, "deptName": "高等教育处", "count": 156 },
      { "deptId": 7, "deptName": "职业教育处", "count": 167 }
    ],
    "monthlyTrend": [
      { "month": "2025-01", "archived": 89, "pending": 12 },
      { "month": "2025-02", "archived": 67, "pending": 8 },
      { "month": "2025-03", "archived": 134, "pending": 15 },
      { "month": "2025-04", "archived": 156, "pending": 18 },
      { "month": "2025-05", "archived": 178, "pending": 22 },
      { "month": "2025-06", "archived": 89, "pending": 45 }
    ]
  },
  "timestamp": 1703123456789
}
```

### 借阅统计数据
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalApplications": 156,
    "totalBorrowed": 89,
    "totalReturned": 78,
    "overdue": 5,
    "byStatus": {
      "applying": 12,
      "borrowed": 11,
      "returned": 78,
      "rejected": 8
    },
    "byDepartment": [
      { "deptId": 1, "deptName": "办公室", "count": 23 },
      { "deptId": 2, "deptName": "教育督导处", "count": 18 },
      { "deptId": 3, "deptName": "人事处", "count": 15 },
      { "deptId": 4, "deptName": "财务处", "count": 12 },
      { "deptId": 5, "deptName": "基础教育处", "count": 28 }
    ],
    "monthlyTrend": [
      { "month": "2025-01", "applications": 12, "approved": 10 },
      { "month": "2025-02", "applications": 8, "approved": 7 },
      { "month": "2025-03", "applications": 15, "approved": 13 },
      { "month": "2025-04", "applications": 18, "approved": 16 },
      { "month": "2025-05", "applications": 22, "approved": 19 },
      { "month": "2025-06", "applications": 25, "approved": 20 }
    ]
  },
  "timestamp": 1703123456789
}
```

## 🏢 组织架构模拟数据

### 部门树形结构
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "浙江省教育厅",
      "code": "ROOT",
      "parentId": null,
      "level": 1,
      "sortOrder": 1,
      "status": "active",
      "children": [
        {
          "id": 2,
          "name": "办公室",
          "code": "BGS",
          "parentId": 1,
          "level": 2,
          "sortOrder": 1,
          "status": "active",
          "children": []
        },
        {
          "id": 3,
          "name": "教育督导处",
          "code": "JYDC",
          "parentId": 1,
          "level": 2,
          "sortOrder": 2,
          "status": "active",
          "children": []
        },
        {
          "id": 4,
          "name": "人事处",
          "code": "RSC",
          "parentId": 1,
          "level": 2,
          "sortOrder": 3,
          "status": "active",
          "children": []
        },
        {
          "id": 5,
          "name": "财务处",
          "code": "CWC",
          "parentId": 1,
          "level": 2,
          "sortOrder": 4,
          "status": "active",
          "children": []
        },
        {
          "id": 6,
          "name": "基础教育处",
          "code": "JJC",
          "parentId": 1,
          "level": 2,
          "sortOrder": 5,
          "status": "active",
          "children": []
        }
      ]
    }
  ],
  "timestamp": 1703123456789
}
```

## 👥 用户管理模拟数据

### 用户列表
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1,
        "username": "admin",
        "realname": "系统管理员",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "dept": {
          "id": 1,
          "name": "办公室"
        },
        "roles": [
          { "id": 1, "name": "系统管理员", "code": "admin" },
          { "id": 2, "name": "档案管理员", "code": "archive_manager" }
        ],
        "status": "active",
        "lastLoginAt": "2025-06-25T09:30:00Z",
        "createdAt": "2024-01-01T00:00:00Z"
      },
      {
        "id": 2,
        "username": "zhangsan",
        "realname": "张三",
        "email": "<EMAIL>",
        "phone": "13800138001",
        "dept": {
          "id": 2,
          "name": "基础教育处"
        },
        "roles": [
          { "id": 3, "name": "普通用户", "code": "user" }
        ],
        "status": "active",
        "lastLoginAt": "2025-06-24T16:45:00Z",
        "createdAt": "2024-03-15T10:20:00Z"
      },
      {
        "id": 3,
        "username": "lisi",
        "realname": "李四",
        "email": "<EMAIL>",
        "phone": "13800138002",
        "dept": {
          "id": 3,
          "name": "人事处"
        },
        "roles": [
          { "id": 3, "name": "普通用户", "code": "user" },
          { "id": 4, "name": "审核员", "code": "reviewer" }
        ],
        "status": "inactive",
        "lastLoginAt": "2025-06-20T14:20:00Z",
        "createdAt": "2024-05-10T08:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 45,
      "totalPages": 5
    }
  },
  "timestamp": 1703123456789
}
```

## ⚙️ 系统配置模拟数据

### 系统配置列表
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "key": "system.title",
      "value": "浙江省教育厅档案管理系统",
      "type": "string",
      "group": "system",
      "description": "系统标题"
    },
    {
      "key": "archive.max_file_size",
      "value": 104857600,
      "type": "number",
      "group": "archive",
      "description": "单个文件最大上传大小（字节）"
    },
    {
      "key": "archive.allowed_file_types",
      "value": ["pdf", "doc", "docx", "xls", "xlsx", "jpg", "png"],
      "type": "json",
      "group": "archive",
      "description": "允许上传的文件类型"
    },
    {
      "key": "security.password_complexity",
      "value": true,
      "type": "boolean",
      "group": "security",
      "description": "是否启用密码复杂度检查"
    },
    {
      "key": "borrowing.max_days",
      "value": 30,
      "type": "number",
      "group": "borrowing",
      "description": "最大借阅天数"
    }
  ],
  "timestamp": 1703123456789
}
```

## 🔌 第三方系统接口模拟数据

### 档案上传成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "档案上传成功",
  "data": {
    "collectionId": 12345,
    "archiveId": 1001,
    "archiveNo": "ZJS-2025-OA-0001",
    "uploadedFiles": [
      {
        "fileId": 2001,
        "fileName": "公文正文.pdf",
        "status": "uploaded"
      },
      {
        "fileId": 2002,
        "fileName": "审批单.pdf",
        "status": "uploaded"
      }
    ],
    "processStatus": "pending_review"
  },
  "timestamp": 1703123456789
}
```

### 批量上传状态查询响应
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "batchId": "batch_20250625_001",
    "totalCount": 50,
    "successCount": 45,
    "failedCount": 3,
    "processingCount": 2,
    "status": "processing",
    "details": [
      {
        "sourceId": "OA_DOC_001",
        "status": "success",
        "archiveId": 1001,
        "message": "上传成功"
      },
      {
        "sourceId": "OA_DOC_002",
        "status": "failed",
        "archiveId": null,
        "message": "文件格式不支持"
      },
      {
        "sourceId": "OA_DOC_003",
        "status": "processing",
        "archiveId": null,
        "message": "正在处理中"
      }
    ]
  },
  "timestamp": 1703123456789
}
```

## 📝 操作日志模拟数据

### 操作日志列表
```json
{
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1001,
        "archive": {
          "id": 15,
          "title": "关于开展中小学教师培训工作的通知",
          "archiveNo": "ZJS-2025-JJC-0015"
        },
        "operator": {
          "id": 5,
          "name": "王五"
        },
        "operationType": "update",
        "operationDetails": "修改档案状态：从'待整理'改为'待归档'",
        "operationTime": "2025-06-25T14:30:00Z",
        "ipAddress": "*************",
        "clientType": "web"
      },
      {
        "id": 1002,
        "archive": {
          "id": 28,
          "title": "中小学教师职业道德规范实施细则",
          "archiveNo": "ZJS-2025-RSC-0028"
        },
        "operator": {
          "id": 8,
          "name": "赵六"
        },
        "operationType": "download",
        "operationDetails": "下载档案文件：实施细则正文.pdf",
        "operationTime": "2025-06-25T10:15:00Z",
        "ipAddress": "*************",
        "clientType": "web"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 2456,
      "totalPages": 246
    }
  },
  "timestamp": 1703123456789
}
```

## ❌ 错误响应示例

### 参数验证失败
```json
{
  "success": false,
  "code": 1001,
  "message": "参数验证失败",
  "data": {
    "errors": [
      {
        "field": "title",
        "message": "标题不能为空"
      },
      {
        "field": "year",
        "message": "年份必须是有效的数字"
      }
    ]
  },
  "timestamp": 1703123456789
}
```

### 权限不足
```json
{
  "success": false,
  "code": 1003,
  "message": "权限不足，无法执行此操作",
  "data": {
    "requiredPermission": "archive:delete",
    "userPermissions": ["archive:read", "archive:write"]
  },
  "timestamp": 1703123456789
}
```

### 资源不存在
```json
{
  "success": false,
  "code": 2001,
  "message": "档案不存在",
  "data": {
    "archiveId": 99999,
    "suggestion": "请检查档案ID是否正确"
  },
  "timestamp": 1703123456789
}
```
