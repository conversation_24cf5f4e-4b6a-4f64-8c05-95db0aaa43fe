# 教育厅档案管理系统 - 开发文档

## 📚 文档概览

本文档集为 Augment AI 在教育厅档案管理系统项目中的开发工作提供全面的指导和规范。

### 📁 文档结构

```
docs/
├── README.md                           # 本文档 - 总览和快速开始
├── development/
│   └── AUGMENT_RULES.md               # Augment 工作规则和用户指南
├── architecture/
│   └── SYSTEM_ARCHITECTURE.md        # 系统架构文档
└── guidelines/
    ├── COMPONENT_GUIDELINES.md       # 组件开发指南
    └── API_DATA_GUIDELINES.md        # API 和数据处理指南
```

## 🚀 快速开始

### 1. 项目技术栈

- **前端框架**: React 18.3.1 + TypeScript
- **构建工具**: Vite 6.3.5
- **UI 组件库**: Radix UI + shadcn/ui
- **样式系统**: Tailwind CSS v4
- **图标库**: Lucide React
- **状态管理**: React Hooks

### 2. 开发环境设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 3. 项目结构理解

```
src/
├── components/              # 组件目录
│   ├── ui/                 # 基础 UI 组件 (Radix UI + shadcn/ui)
│   ├── Layout.tsx          # 主布局组件
│   ├── Sidebar.tsx         # 侧边栏导航
│   └── [Module].tsx        # 业务模块组件
├── assets/                 # 静态资源
├── App.tsx                 # 根组件 (状态管理中心)
├── main.tsx               # 应用入口
└── index.css              # 全局样式 (Tailwind CSS v4)
```

## 📋 核心开发规则

### 1. 组件开发规范

```typescript
// ✅ 标准组件结构
interface ComponentProps {
  title: string;
  onAction?: (id: string) => void;
}

export function Component({ title, onAction }: ComponentProps) {
  const [state, setState] = useState<string>('');
  
  return (
    <div className="component-container">
      {/* 组件内容 */}
    </div>
  );
}
```

### 2. 状态管理规范

- **全局状态**: 在 `App.tsx` 中使用 `useState` 管理
- **本地状态**: 组件内部使用 `useState`
- **状态提升**: 共享状态提升到最近的共同父组件

### 3. 样式开发规范

```typescript
// ✅ 使用 Tailwind CSS 类名
<div className="flex items-center gap-4 p-6 bg-white rounded-lg shadow-md">

// ✅ 使用设计令牌
<div className="bg-primary text-primary-foreground">

// ✅ 响应式设计
<div className="w-full md:w-1/2 lg:w-1/3">
```

### 4. TypeScript 规范

```typescript
// ✅ 严格类型定义
interface User {
  name: string;
  organization: string;
  permissions?: string[];
}

// ✅ 泛型使用
const [data, setData] = useState<Archive[]>([]);
```

## 🏗️ 架构要点

### 1. 模块化架构

项目采用模块化架构，每个业务功能对应一个独立组件：

- `ArchiveCollection` - 档案收集
- `ManualEntry` - 手动录入
- `ArchiveOrganization` - 档案整理
- `ArchiveSearch` - 综合检索
- `BorrowingManagement` - 借阅管理
- `DataAnalysis` - 数据分析
- `SystemManagement` - 系统管理

### 2. 状态流向

```
User Interaction → Event Handler → State Update → Re-render → UI Update
```

### 3. 组件通信

```typescript
// 父子组件通信
<ChildComponent 
  data={parentData} 
  onAction={handleAction} 
/>

// 兄弟组件通信 (通过共同父组件)
App State → Sidebar → Content Area
```

## 🎨 UI/UX 开发要点

### 1. 设计系统

使用预定义的设计令牌确保一致性：

```css
:root {
  --primary: #030213;
  --secondary: oklch(.95 .0058 264.53);
  --background: #fff;
  --foreground: oklch(.145 0 0);
  --border: #0000001a;
}
```

### 2. 组件库使用

```typescript
// ✅ 优先使用 UI 组件
import { Button } from './ui/button';
import { Card, CardContent, CardHeader } from './ui/card';
import { Dialog, DialogContent, DialogHeader } from './ui/dialog';
```

### 3. 响应式设计

采用移动优先的响应式设计：

```typescript
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
```

## 📡 数据处理要点

### 1. API 服务模式

```typescript
class ArchiveService {
  async getArchives(params: PaginationParams): Promise<ApiResponse<Archive[]>> {
    // API 调用逻辑
  }
}

export const archiveService = new ArchiveService();
```

### 2. 数据获取 Hooks

```typescript
function useApi<T>(apiCall: () => Promise<ApiResponse<T>>) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 实现逻辑
  
  return { data, loading, error, refetch };
}
```

### 3. 数据验证

```typescript
function validateArchive(data: Partial<Archive>) {
  const errors: Record<string, string> = {};
  
  if (!data.title?.trim()) {
    errors.title = '标题不能为空';
  }
  
  return { isValid: Object.keys(errors).length === 0, errors };
}
```

## 🧪 测试规范

### 1. 组件测试

```typescript
import { render, screen, fireEvent } from '@testing-library/react';

test('component handles user interaction', () => {
  const handleClick = jest.fn();
  render(<Component onClick={handleClick} />);
  
  fireEvent.click(screen.getByRole('button'));
  expect(handleClick).toHaveBeenCalled();
});
```

### 2. 异步测试

```typescript
import { waitFor } from '@testing-library/react';

test('loads data asynchronously', async () => {
  render(<DataComponent />);
  
  await waitFor(() => {
    expect(screen.getByText('Loaded Data')).toBeInTheDocument();
  });
});
```

## 🚀 性能优化

### 1. 组件优化

```typescript
// 使用 memo 优化重渲染
export const ExpensiveComponent = memo(({ data }: Props) => {
  return <div>{/* 复杂渲染逻辑 */}</div>;
});

// 使用 useCallback 优化函数引用
const handleClick = useCallback((id: string) => {
  onAction(id);
}, [onAction]);
```

### 2. 代码分割

```typescript
// 懒加载大型组件
const DataAnalysis = lazy(() => import('./DataAnalysis'));

<Suspense fallback={<div>Loading...</div>}>
  <DataAnalysis />
</Suspense>
```

## 🔒 安全规范

### 1. 输入验证

```typescript
const handleSearchSubmit = (e: React.FormEvent) => {
  e.preventDefault();
  const cleanQuery = searchQuery.trim();
  if (cleanQuery && onSearch) {
    onSearch(cleanQuery);
  }
};
```

### 2. 权限控制

```typescript
{currentUser?.permissions?.includes('admin') && (
  <AdminOnlyComponent />
)}
```

## 📚 文档规范

### 1. 组件文档

```typescript
/**
 * 档案收集组件 - 处理电子档案采集和手动录入
 * 
 * @param onViewArchive - 查看档案详情的回调函数
 * @param searchQuery - 可选的搜索查询字符串
 */
export function ArchiveCollection({ onViewArchive, searchQuery }: Props) {
  // 实现
}
```

### 2. 代码注释

```typescript
// 处理模块切换逻辑，确保正确的状态同步
const renderMainContent = () => {
  switch (activeModule) {
    case 'electronic-collection':
      return <ArchiveCollection onViewArchive={handleViewArchive} />;
    // ... 其他 case
  }
};
```

## 🎯 最佳实践总结

1. **保持组件单一职责** - 每个组件只负责一个功能模块
2. **使用 TypeScript 严格模式** - 确保类型安全
3. **遵循设计系统** - 保持 UI 一致性
4. **状态管理简单化** - 优先使用 React 内置状态管理
5. **性能优先** - 合理使用优化手段
6. **可访问性** - 利用 Radix UI 的无障碍特性
7. **响应式设计** - 移动优先的设计理念
8. **代码可读性** - 清晰的命名和适当的注释

## 📖 详细文档链接

- [Augment 工作规则和用户指南](./development/AUGMENT_RULES.md)
- [系统架构文档](./architecture/SYSTEM_ARCHITECTURE.md)
- [组件开发指南](./guidelines/COMPONENT_GUIDELINES.md)
- [API 和数据处理指南](./guidelines/API_DATA_GUIDELINES.md)

## 🤝 贡献指南

在开始开发之前，请务必阅读相关的文档：

1. 首先阅读 [Augment 工作规则](./development/AUGMENT_RULES.md)
2. 了解 [系统架构](./architecture/SYSTEM_ARCHITECTURE.md)
3. 根据开发任务查阅相应的指南文档
4. 遵循代码规范和最佳实践
5. 编写必要的测试和文档

这些文档将帮助 Augment 在项目中保持高质量的代码和一致的开发体验。
