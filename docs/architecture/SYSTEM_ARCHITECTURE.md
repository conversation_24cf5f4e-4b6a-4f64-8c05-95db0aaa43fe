# 系统架构文档

## 📐 整体架构

### 技术栈

- **前端框架**: React 18.3.1 + TypeScript
- **构建工具**: Vite 6.3.5
- **UI 组件库**: Radix UI + shadcn/ui
- **样式系统**: Tailwind CSS v4
- **图标库**: Lucide React
- **图表库**: Recharts
- **状态管理**: React Hooks (useState)
- **表单处理**: React Hook Form
- **日期处理**: React Day Picker

### 项目结构

```
sjyt-dms/
├── docs/                    # 项目文档
│   ├── development/         # 开发相关文档
│   ├── architecture/        # 架构文档
│   └── guidelines/          # 指南文档
├── src/
│   ├── components/          # 组件目录
│   │   ├── ui/             # 基础 UI 组件
│   │   ├── figma/          # Figma 相关组件
│   │   ├── Layout.tsx      # 主布局组件
│   │   ├── Sidebar.tsx     # 侧边栏组件
│   │   ├── Login.tsx       # 登录组件
│   │   ├── ArchiveCollection.tsx    # 档案收集
│   │   ├── ManualEntry.tsx          # 手动录入
│   │   ├── ArchiveOrganization.tsx  # 档案整理
│   │   ├── ArchiveSearch.tsx        # 档案检索
│   │   ├── BorrowingManagement.tsx  # 借阅管理
│   │   ├── DataAnalysis.tsx         # 数据分析
│   │   ├── SystemManagement.tsx     # 系统管理
│   │   └── ArchiveDetail.tsx        # 档案详情
│   ├── assets/             # 静态资源
│   ├── styles/             # 样式文件
│   ├── App.tsx            # 根组件
│   ├── main.tsx           # 应用入口
│   └── index.css          # 全局样式
├── build/                  # 构建输出
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript 配置
├── tsconfig.node.json     # Node.js TypeScript 配置
└── vite.config.ts         # Vite 配置
```

## 🏛️ 组件架构

### 1. 应用层级结构

```
App (根组件)
├── Login (未登录状态)
└── Layout (已登录状态)
    ├── Header (顶部导航)
    │   ├── Logo
    │   ├── SearchBar
    │   └── UserProfile
    └── Main Content
        ├── Sidebar (侧边栏导航)
        └── Content Area (主内容区)
            ├── ArchiveCollection
            ├── ManualEntry
            ├── ArchiveOrganization
            ├── ArchiveSearch
            ├── BorrowingManagement
            ├── DataAnalysis
            ├── SystemManagement
            └── ArchiveDetail
```

### 2. 状态管理架构

```typescript
// App.tsx 中的全局状态
interface AppState {
  isLoggedIn: boolean;           // 登录状态
  activeModule: string;          // 当前激活模块
  currentUser: User | null;      // 当前用户信息
  searchQuery: string;           // 搜索查询
  currentArchiveId: string | null; // 当前档案ID
}

// 状态流向
App State → Props → Child Components → Event Handlers → State Updates
```

### 3. 模块路由映射

```typescript
const moduleRoutes = {
  // 档案收集模块
  'electronic-collection': ArchiveCollection,
  'manual-entry': ManualEntry,
  
  // 档案整理模块
  'pending-archive': ArchiveOrganization,
  'archived': ArchiveOrganization,
  
  // 检索模块
  'comprehensive-search': ArchiveSearch,
  
  // 借阅管理模块
  'pending-tasks': BorrowingManagement,
  'completed-tasks': BorrowingManagement,
  'finished-tasks': BorrowingManagement,
  'my-applications': BorrowingManagement,
  'borrowing-records': BorrowingManagement,
  
  // 数据分析模块
  'statistics': DataAnalysis,
  
  // 系统管理模块
  'archive-directory': SystemManagement,
  'cataloging-config': SystemManagement,
  'user-management': SystemManagement,
  'permission-management': SystemManagement,
  
  // 详情页面
  'archive-detail': ArchiveDetail,
};
```

## 🎨 UI 组件架构

### 1. 组件层次结构

```
UI Components (src/components/ui/)
├── Layout Components
│   ├── card.tsx           # 卡片容器
│   ├── sheet.tsx          # 侧边面板
│   ├── dialog.tsx         # 对话框
│   └── tabs.tsx           # 标签页
├── Form Components
│   ├── input.tsx          # 输入框
│   ├── textarea.tsx       # 文本域
│   ├── select.tsx         # 选择器
│   ├── checkbox.tsx       # 复选框
│   ├── radio-group.tsx    # 单选组
│   └── form.tsx           # 表单容器
├── Navigation Components
│   ├── button.tsx         # 按钮
│   ├── navigation-menu.tsx # 导航菜单
│   ├── breadcrumb.tsx     # 面包屑
│   └── pagination.tsx     # 分页
├── Data Display
│   ├── table.tsx          # 表格
│   ├── badge.tsx          # 徽章
│   ├── avatar.tsx         # 头像
│   ├── progress.tsx       # 进度条
│   └── chart.tsx          # 图表
└── Feedback Components
    ├── alert.tsx          # 警告
    ├── sonner.tsx         # 通知
    ├── tooltip.tsx        # 工具提示
    └── skeleton.tsx       # 骨架屏
```

### 2. 设计系统

```css
/* 设计令牌 (index.css) */
:root {
  /* 颜色系统 */
  --primary: #030213;
  --secondary: oklch(.95 .0058 264.53);
  --background: #fff;
  --foreground: oklch(.145 0 0);
  --muted: #ececf0;
  --accent: #e9ebef;
  --destructive: #d4183d;
  --border: #0000001a;
  
  /* 间距系统 */
  --spacing: .25rem;
  
  /* 字体系统 */
  --font-sans: ui-sans-serif, system-ui, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo;
  
  /* 圆角系统 */
  --radius: 0.5rem;
  --radius-xs: .125rem;
}
```

## 🔄 数据流架构

### 1. 单向数据流

```
User Interaction → Event Handler → State Update → Re-render → UI Update
```

### 2. 组件通信模式

```typescript
// 父子组件通信
interface ParentToChild {
  data: any;
  onAction: (param: any) => void;
}

// 兄弟组件通信 (通过共同父组件)
App State → Sidebar (activeModule) → Content (renderMainContent)
```

### 3. 事件处理模式

```typescript
// 标准事件处理模式
const handleAction = (param: any) => {
  // 1. 验证输入
  if (!param) return;
  
  // 2. 更新状态
  setState(newState);
  
  // 3. 触发副作用 (可选)
  onCallback?.(param);
};
```

## 🛠️ 构建架构

### 1. Vite 配置

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    alias: {
      // 版本化别名 (用于依赖管理)
      'vaul@1.1.2': 'vaul',
      'sonner@2.0.3': 'sonner',
      // ... 其他别名
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    target: 'esnext',
    outDir: 'build',
  },
  server: {
    port: 3000,
    open: true,
  },
});
```

### 2. TypeScript 配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    "strict": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

## 🔐 安全架构

### 1. 认证流程

```
用户登录 → 验证凭据 → 设置用户状态 → 权限检查 → 页面访问
```

### 2. 权限控制

```typescript
// 基于角色的访问控制
interface User {
  name: string;
  organization: string;
  permissions?: string[];
}

// 条件渲染权限控制
{currentUser?.permissions?.includes('admin') && (
  <AdminOnlyComponent />
)}
```

## 📱 响应式架构

### 1. 断点系统

```css
/* Tailwind CSS 断点 */
sm: 640px   /* 小屏幕 */
md: 768px   /* 中等屏幕 */
lg: 1024px  /* 大屏幕 */
xl: 1280px  /* 超大屏幕 */
2xl: 1536px /* 超超大屏幕 */
```

### 2. 响应式组件模式

```typescript
// 移动优先设计
<div className="w-full md:w-auto lg:w-96">
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
<div className="text-sm md:text-base lg:text-lg">
```

## 🚀 性能架构

### 1. 代码分割策略

```typescript
// 路由级别的代码分割
const DataAnalysis = lazy(() => import('./DataAnalysis'));
const SystemManagement = lazy(() => import('./SystemManagement'));

// 使用 Suspense 包装
<Suspense fallback={<LoadingSpinner />}>
  <DataAnalysis />
</Suspense>
```

### 2. 优化策略

- **组件级优化**: React.memo, useCallback, useMemo
- **资源优化**: 图片懒加载, 代码分割
- **渲染优化**: 虚拟滚动, 分页加载
- **缓存策略**: 浏览器缓存, 组件缓存

## 📊 监控和调试

### 1. 开发工具

- **React DevTools**: 组件树调试
- **Vite DevTools**: 构建分析
- **TypeScript**: 类型检查
- **ESLint**: 代码质量检查

### 2. 性能监控

```typescript
// 性能监控点
console.time('Component Render');
// 组件渲染逻辑
console.timeEnd('Component Render');
```

这个架构文档为 Augment 提供了系统的技术架构理解，有助于在开发过程中做出正确的技术决策。
