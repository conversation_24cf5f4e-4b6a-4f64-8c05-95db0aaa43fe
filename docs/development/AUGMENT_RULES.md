# 教育厅档案管理系统 - Augment 工作规则和用户指南

## 📋 项目概述

这是一个基于 React + TypeScript + Vite 的现代化档案管理系统，使用 Radix UI 组件库和 Tailwind CSS v4 构建。项目采用模块化架构，包含档案收集、整理、检索、借阅、分析和系统管理等核心功能。

## 🏗️ 架构规则

### 1. 项目结构规范

```
src/
├── components/           # 业务组件
│   ├── ui/              # 基础 UI 组件 (Radix UI + shadcn/ui)
│   ├── figma/           # Figma 相关组件
│   ├── Layout.tsx       # 布局组件
│   ├── Sidebar.tsx      # 侧边栏导航
│   └── [Module].tsx     # 业务模块组件
├── assets/              # 静态资源
├── styles/              # 样式文件
├── main.tsx            # 应用入口
├── App.tsx             # 根组件
└── index.css           # 全局样式 (Tailwind CSS v4)
```

### 2. 状态管理规范

- **全局状态**: 使用 React useState 在 App.tsx 中管理
- **关键状态变量**:
  - `isLoggedIn`: 登录状态
  - `activeModule`: 当前激活模块
  - `currentUser`: 当前用户信息
  - `searchQuery`: 搜索查询
  - `currentArchiveId`: 当前档案ID

### 3. 路由和导航规范

- **无路由库**: 使用条件渲染和状态切换实现页面导航
- **模块标识符**:
  - `electronic-collection`: 电子档案采集
  - `manual-entry`: 档案录入
  - `archive-detail`: 档案详情
  - `comprehensive-search`: 综合检索
  - `statistics`: 数据分析
  - 等等...

## 🎨 UI/UX 开发规则

### 1. 组件库使用规范

**基础组件**: 优先使用 `src/components/ui/` 中的组件
```typescript
// ✅ 正确使用
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardHeader } from './ui/card';
```

**Radix UI 组件**: 通过 ui 组件封装使用
```typescript
// ✅ 推荐方式
import { Dialog, DialogContent, DialogHeader } from './ui/dialog';

// ❌ 避免直接使用
import * as Dialog from '@radix-ui/react-dialog';
```

### 2. 样式系统规范

**Tailwind CSS v4**: 使用最新版本的 Tailwind
```typescript
// ✅ 使用 Tailwind 类名
<div className="flex items-center gap-4 p-6 bg-white rounded-lg shadow-md">

// ✅ 使用 CSS 变量 (定义在 index.css 中)
<div className="bg-primary text-primary-foreground">
```

**颜色系统**: 使用预定义的设计令牌
```css
/* 主要颜色变量 */
--primary: #030213;
--secondary: oklch(.95 .0058 264.53);
--background: #fff;
--foreground: oklch(.145 0 0);
--border: #0000001a;
```

### 3. 响应式设计规范

```typescript
// ✅ 移动优先的响应式设计
<div className="w-full md:w-auto lg:w-96">
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
```

## 🔧 开发工作流规则

### 1. 组件开发规范

**函数组件**: 使用函数组件和 Hooks
```typescript
// ✅ 标准组件结构
interface ComponentProps {
  title: string;
  onAction?: (id: string) => void;
}

export function Component({ title, onAction }: ComponentProps) {
  const [state, setState] = useState<string>('');
  
  return (
    <div className="component-container">
      {/* 组件内容 */}
    </div>
  );
}
```

**Props 接口**: 明确定义组件接口
```typescript
// ✅ 清晰的 Props 定义
interface ArchiveCollectionProps {
  onViewArchive: (archiveId: string) => void;
  searchQuery?: string;
  filters?: ArchiveFilter[];
}
```

### 2. 状态管理规范

**本地状态**: 组件内部状态使用 useState
```typescript
// ✅ 本地状态管理
const [isExpanded, setIsExpanded] = useState(false);
const [formData, setFormData] = useState<FormData>({});
```

**状态提升**: 共享状态提升到父组件
```typescript
// ✅ 在 App.tsx 中管理全局状态
const [activeModule, setActiveModule] = useState('electronic-collection');

// ✅ 通过 props 传递状态和处理函数
<Sidebar 
  activeModule={activeModule} 
  onModuleChange={setActiveModule} 
/>
```

### 3. 事件处理规范

```typescript
// ✅ 事件处理函数命名规范
const handleSearch = (query: string) => {
  setSearchQuery(query);
  setActiveModule('comprehensive-search');
};

const handleViewArchive = (archiveId: string) => {
  setCurrentArchiveId(archiveId);
  setActiveModule('archive-detail');
};
```

## 📝 代码质量规则

### 1. TypeScript 规范

**严格类型**: 启用严格模式
```typescript
// ✅ 明确的类型定义
interface User {
  name: string;
  organization: string;
  permissions?: string[];
}

// ✅ 泛型使用
const [data, setData] = useState<ArchiveData[]>([]);
```

**类型导入**: 使用 type 关键字导入类型
```typescript
// ✅ 类型导入
import type { ReactNode } from 'react';
import type { VariantProps } from 'class-variance-authority';
```

### 2. 导入规范

```typescript
// ✅ 导入顺序
// 1. React 相关
import { useState, useEffect } from 'react';

// 2. 第三方库
import { Search, Power } from 'lucide-react';

// 3. 本地组件
import { Button } from './ui/button';
import { Layout } from './Layout';

// 4. 类型导入
import type { ComponentProps } from './types';
```

### 3. 文件命名规范

```
✅ 组件文件: PascalCase.tsx
- ArchiveCollection.tsx
- SystemManagement.tsx
- Layout.tsx

✅ UI 组件: kebab-case.tsx
- button.tsx
- input.tsx
- dialog.tsx

✅ 工具文件: camelCase.ts
- utils.ts
- constants.ts
```

## 🚀 性能优化规则

### 1. 组件优化

```typescript
// ✅ 使用 React.memo 优化重渲染
export const ExpensiveComponent = React.memo(({ data }: Props) => {
  return <div>{/* 复杂渲染逻辑 */}</div>;
});

// ✅ 使用 useCallback 优化函数引用
const handleClick = useCallback((id: string) => {
  onAction(id);
}, [onAction]);
```

### 2. 懒加载

```typescript
// ✅ 动态导入大型组件
const DataAnalysis = lazy(() => import('./DataAnalysis'));

// 在使用时包装 Suspense
<Suspense fallback={<div>Loading...</div>}>
  <DataAnalysis />
</Suspense>
```

## 🧪 测试规范

### 1. 组件测试

```typescript
// ✅ 测试组件渲染和交互
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './button';

test('button handles click events', () => {
  const handleClick = jest.fn();
  render(<Button onClick={handleClick}>Click me</Button>);
  
  fireEvent.click(screen.getByRole('button'));
  expect(handleClick).toHaveBeenCalledTimes(1);
});
```

## 🔒 安全规范

### 1. 用户输入处理

```typescript
// ✅ 输入验证和清理
const handleSearchSubmit = (e: React.FormEvent) => {
  e.preventDefault();
  const cleanQuery = searchQuery.trim();
  if (cleanQuery && onSearch) {
    onSearch(cleanQuery);
  }
};
```

### 2. 权限控制

```typescript
// ✅ 基于用户权限的条件渲染
{currentUser?.permissions?.includes('admin') && (
  <SystemManagement />
)}
```

## 📚 文档规范

### 1. 组件文档

```typescript
/**
 * 档案收集组件 - 处理电子档案采集和手动录入
 * 
 * @param onViewArchive - 查看档案详情的回调函数
 * @param searchQuery - 可选的搜索查询字符串
 * @param filters - 可选的过滤器数组
 */
export function ArchiveCollection({ 
  onViewArchive, 
  searchQuery, 
  filters 
}: ArchiveCollectionProps) {
  // 组件实现
}
```

### 2. 复杂逻辑注释

```typescript
// 处理模块切换逻辑，确保正确的状态同步
const renderMainContent = () => {
  switch (activeModule) {
    case 'electronic-collection':
      return <ArchiveCollection onViewArchive={handleViewArchive} />;
    // ... 其他 case
  }
};
```

## 🛠️ 开发工具配置

### 1. Vite 配置要点

```typescript
// vite.config.ts 关键配置
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    target: 'esnext',
    outDir: 'build',
  },
});
```

### 2. 包管理

```bash
# ✅ 使用 npm 管理依赖
npm install <package>
npm run dev
npm run build

# ❌ 避免混用包管理器
```

## 🎯 最佳实践总结

1. **保持组件单一职责**: 每个组件只负责一个功能模块
2. **使用 TypeScript 严格模式**: 确保类型安全
3. **遵循 Radix UI + Tailwind 设计系统**: 保持 UI 一致性
4. **状态管理简单化**: 优先使用 React 内置状态管理
5. **性能优先**: 合理使用 memo、callback 等优化手段
6. **可访问性**: 利用 Radix UI 的无障碍特性
7. **响应式设计**: 移动优先的设计理念
8. **代码可读性**: 清晰的命名和适当的注释

这些规则和指南将帮助 Augment 在这个项目中保持代码质量、一致性和可维护性。
