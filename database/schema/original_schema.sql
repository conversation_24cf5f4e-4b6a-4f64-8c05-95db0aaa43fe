
DROP TABLE IF EXISTS sys_dept;
CREATE TABLE sys_dept(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 机构 ID',
    `name` VARCHAR(256) NOT NULL COMMENT '机构名称',
    `parent_id` BIGINT COMMENT '上级机构 ID',
    `code` VARCHAR(64) COMMENT '机构编码',
    PRIMARY KEY (`id`)
) COMMENT ' 组织机构及处室信息表 ';


DROP TABLE IF EXISTS sys_user;
CREATE TABLE sys_user(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 用户 ID',
    `username` VARCHAR(50) NOT NULL COMMENT ' 登录账号（唯一）',
    `realname` VARCHAR(50) NOT NULL COMMENT ' 真实姓名（用于审批、操作日志）',
    `dept_id` BIGINT COMMENT ' 所属机构 ID',
    PRIMARY KEY (`id`)
) COMMENT ' 系统用户表 ';


DROP TABLE IF EXISTS archive_directory;
CREATE TABLE archive_directory(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 目录 ID',
    `name` VARCHAR(256) NOT NULL COMMENT '目录名称',
    `parent_id` BIGINT COMMENT '父目录 ID（顶级目录为 NULL）',
    `level` BIGINT NOT NULL COMMENT '目录层级',
    `sort_order` BIGINT DEFAULT 0 COMMENT '排序号（控制前端显示顺序）',
    `status` VARCHAR(64) COMMENT ' 状态：启用 / 停用 ',
    `remark` TEXT COMMENT '备注',
    `is_deleted` INT DEFAULT 0 COMMENT '删除标识（1为删除）',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT ' 档案多级目录表 ';


DROP TABLE IF EXISTS third_party_system;
CREATE TABLE third_party_system(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 第三方系统 ID',
    `system_code` VARCHAR(50) NOT NULL COMMENT '系统编码（档案系统分配，如：OA_SYS_001）',
    `system_name` VARCHAR(100) NOT NULL COMMENT ' 系统名称（如：XX 公司 OA 系统）',
    `system_type` VARCHAR(50) NOT NULL COMMENT ' 系统类型（OA/ERP/ 业务子系统）',
    `app_id` VARCHAR(100) NOT NULL COMMENT ' 接口认证 APPID（唯一标识）',
    `app_secret` VARCHAR(256) NOT NULL COMMENT ' 接口密钥（加密存储，用于签名验证）',
    `allowed_apis` TEXT NOT NULL COMMENT '允许调用的接口列表（如：["/api/archive/upload","/api/file/upload"]）',
    `status` VARCHAR(128) COMMENT ' 状态：启用 / 禁用 / 过期 ',
    `expire_time` DATETIME COMMENT '认证过期时间（NULL 表示永久有效）',
    `contact_person` VARCHAR(50) NOT NULL COMMENT ' 对接人姓名 ',
    `contact_phone` VARCHAR(20) NOT NULL COMMENT ' 对接人电话 ',
    `contact_email` VARCHAR(100) COMMENT ' 对接人邮箱 ',
    `remark` TEXT COMMENT '备注',
    `is_deleted` INT DEFAULT 0 COMMENT '删除标识（1为删除）',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT '第三方系统注册表';


DROP TABLE IF EXISTS electronic_archive;
CREATE TABLE electronic_archive(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 档案系统唯一 ID',
    `collection_id` BIGINT COMMENT '元数据id',
    `fonds_no` VARCHAR(50) NOT NULL COMMENT ' 全宗号 ',
    `archive_no` VARCHAR(100) NOT NULL COMMENT '档号（唯一，如：全宗号 - 年度 - 目录号 - 序号）',
    `year` BIGINT NOT NULL COMMENT '年度（对应目录层级中的年度，如 2022）',
    `dept_id` BIGINT NOT NULL COMMENT '组织机构 ID（所属处室）',
    `dept_name` VARCHAR(256) NOT NULL COMMENT '组织机构名称（冗余）',
    `directory_id` BIGINT NOT NULL COMMENT '所属末级目录 ID',
    `file_title` VARCHAR(2048) COMMENT '案卷题名',
    `directory_no` VARCHAR(256) COMMENT '目录号',
    `archive_type` VARCHAR(256) COMMENT '档案类型',
    `file_no` VARCHAR(64) COMMENT '案卷号',
    `library_file_no` VARCHAR(256) COMMENT '馆编案卷号',
    `page_number` VARCHAR(64) COMMENT '张 (页) 号',
    `classification_no` VARCHAR(64) COMMENT '分类号',
    `serial_no` BIGINT NOT NULL COMMENT '序号（同目录同年度下的排序）',
    `doc_no` VARCHAR(100) COMMENT '文件编号',
    `title` VARCHAR(255) NOT NULL COMMENT '正题名（文件标题）',
    `responsible_person` VARCHAR(100) COMMENT '责任人（文件起草人 / 负责人）',
    `form_time` DATE NOT NULL COMMENT '形成时间（文件生成日期）',
    `retention_period` VARCHAR(64) NOT NULL COMMENT '保管期限：永久 / 30 年/ 10 年',
    `security_level` VARCHAR(64) NOT NULL COMMENT ' 密级：公开 / 秘密 / 机密 / 绝密 ',
    `page_count` BIGINT DEFAULT 0 COMMENT ' 文件页数 ',
    `database_level` VARCHAR(64) COMMENT '数据库级别',
    `organization_name` VARCHAR(256) COMMENT '所属机构',
    `draft_version` VARCHAR(64) COMMENT '稿本',
    `carrier_type` VARCHAR(64) COMMENT '载体类型',
    `carrier_specification` VARCHAR(256) COMMENT '载体规格',
    `carrier_quantity` BIGINT COMMENT '载体数量',
    `person_involved` VARCHAR(2048) COMMENT ' 文件所涉人名（相关人员）',
    `keywords` VARCHAR(2048) COMMENT ' 关键词（用于检索）',
    `subject_terms` VARCHAR(2048) COMMENT ' 主题词（规范检索用词）',
    `sort_order` BIGINT COMMENT ' 档案排序号（同目录下显示顺序）',
    `box_no` VARCHAR(50) COMMENT '盒号（对应实体档案盒编号，电子档案可冗余）',
    `note_text` TEXT COMMENT '备注全文（详细说明）',
    `fulltext_recognition` VARCHAR(64) COMMENT '全文识别状态：已识别 / 未识别 / 处理中',
    `note` TEXT COMMENT '附注（补充说明信息）',
    `archive_status` VARCHAR(50) COMMENT '整理状态：待整理/待归档/不归档/暂不归档',
    `status` VARCHAR(50) COMMENT '档案状态：有效 / 归档 / 销毁',
    `source_system_code` VARCHAR(50) COMMENT '来源系统编码（如第三方系统上传，关联 third_party_system.system_code）',
    `source_system_name` VARCHAR(50) COMMENT '来源系统名称（如第三方系统上传，关联 third_party_system.system_name）',
    `creator` VARCHAR(256) COMMENT '创建人姓名',
    `remarks` TEXT COMMENT '备注',
    `is_deleted` INT DEFAULT 0 COMMENT '删除标识（1为删除）',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT '电子档案整理库';


DROP TABLE IF EXISTS electronic_file;
CREATE TABLE electronic_file(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 文件 ID',
    `archive_id` BIGINT NOT NULL COMMENT '关联档案 ID（电子档案主表）',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名（如：XX 通知_定稿.pdf）',
    `file_category` VARCHAR(128) COMMENT '文件大类：主文件 / 审签单 / 附件 / 相关材料',
    `file_subtype` VARCHAR(50) COMMENT '文件子类型（主文件：clean_draft - 清稿、marked_draft - 花脸稿、final_version - 盖章文件；审签单：approval_sheet - 审批单）',
    `upload_source` VARCHAR(128) COMMENT '上传来源：手动上传 / 第三方系统上传',
    `is_deleted` INT DEFAULT 0 COMMENT '删除标识（1为删除）',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT '电子文件及附件表';


DROP TABLE IF EXISTS archive_collection;
CREATE TABLE archive_collection(
    `collection_id` BIGINT AUTO_INCREMENT COMMENT ' 采集记录 ID',
    `source_id` VARCHAR(100) COMMENT '来源系统原始 ID（第三方系统中的文件 ID，便于对账）',
    `original_request_params` TEXT COMMENT '原始参数',
    `param_check_result` VARCHAR(50) COMMENT '完整性校验状态',
    `business_status` VARCHAR(128) COMMENT '业务处理状态',
    `fonds_no` VARCHAR(50) NOT NULL COMMENT ' 全宗号 ',
    `archive_no` VARCHAR(100) NOT NULL COMMENT '档号（唯一，如：全宗号 - 年度 - 目录号 - 序号）',
    `year` BIGINT NOT NULL COMMENT '年度（对应目录层级中的年度，如 2022）',
    `dept_id` BIGINT NOT NULL COMMENT '组织机构 ID（所属处室）',
    `dept_name` VARCHAR(256) NOT NULL COMMENT '组织机构名称（冗余）',
    `directory_id` BIGINT NOT NULL COMMENT '所属末级目录 ID',
    `file_title` VARCHAR(2048) COMMENT '案卷题名',
    `directory_no` VARCHAR(256) COMMENT '目录号',
    `archive_type` VARCHAR(256) COMMENT '档案类型',
    `file_no` VARCHAR(64) COMMENT '案卷号',
    `library_file_no` VARCHAR(256) COMMENT '馆编案卷号',
    `page_number` VARCHAR(64) COMMENT '张 (页) 号',
    `classification_no` VARCHAR(64) COMMENT '分类号',
    `serial_no` BIGINT NOT NULL COMMENT '序号（同目录同年度下的排序）',
    `doc_no` VARCHAR(100) COMMENT '文件编号',
    `title` VARCHAR(255) NOT NULL COMMENT '正题名（文件标题）',
    `responsible_person` VARCHAR(100) COMMENT '责任人（文件起草人 / 负责人）',
    `form_time` DATE NOT NULL COMMENT '形成时间（文件生成日期）',
    `retention_period` VARCHAR(64) NOT NULL COMMENT '保管期限：永久 / 30 年/ 10 年',
    `security_level` VARCHAR(64) NOT NULL COMMENT ' 密级：公开 / 秘密 / 机密 / 绝密 ',
    `page_count` BIGINT DEFAULT 0 COMMENT ' 文件页数 ',
    `database_level` VARCHAR(64) COMMENT '数据库级别',
    `organization_name` VARCHAR(256) COMMENT '所属机构',
    `draft_version` VARCHAR(64) COMMENT '稿本',
    `carrier_type` VARCHAR(64) COMMENT '载体类型',
    `carrier_specification` VARCHAR(256) COMMENT '载体规格',
    `carrier_quantity` BIGINT COMMENT '载体数量',
    `person_involved` VARCHAR(2048) COMMENT ' 文件所涉人名（相关人员）',
    `keywords` VARCHAR(2048) COMMENT ' 关键词（用于检索）',
    `subject_terms` VARCHAR(2048) COMMENT ' 主题词（规范检索用词）',
    `sort_order` BIGINT COMMENT ' 档案排序号（同目录下显示顺序）',
    `box_no` VARCHAR(50) COMMENT '盒号（对应实体档案盒编号，电子档案可冗余）',
    `note_text` TEXT COMMENT '备注全文（详细说明）',
    `fulltext_recognition` VARCHAR(64) COMMENT '全文识别状态：已识别 / 未识别 / 处理中',
    `note` TEXT COMMENT '附注（补充说明信息）',
    `archive_status` VARCHAR(50) COMMENT '整理状态：待整理/待归档/不归档/暂不归档',
    `status` VARCHAR(50) COMMENT '档案状态：有效 / 归档 / 销毁',
    `source_system_id` VARCHAR(50) COMMENT '来源系统编码（如第三方系统上传，关联 third_party_system.system_code）',
    `source_system_name` VARCHAR(50) COMMENT '来源系统名称（如第三方系统上传，关联 third_party_system.system_name）',
    `filter_status` VARCHAR(64) COMMENT '整理状态',
    `filter_operator_username` VARCHAR(64) COMMENT '整理人用户名',
    `filter_operator` VARCHAR(64) COMMENT '整理人姓名',
    `filter_time` DATETIME COMMENT '整理时间',
    `synced_archive_id` BIGINT COMMENT '已同步档案整理库id',
    `collection_time` DATETIME COMMENT '数据采集时间',
    `collection_batch_no` VARCHAR(64) COMMENT '数据采集批次',
    `is_deleted` INT DEFAULT 0 COMMENT '删除标识（1为删除）',
    PRIMARY KEY (`collection_id`)
) COMMENT '档案数据采集库';


DROP TABLE IF EXISTS archive_business_extension;
CREATE TABLE archive_business_extension(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 扩展信息 ID',
    `archive_id` BIGINT NOT NULL COMMENT ' 关联档案 ID',
    `field_name` VARCHAR(100) NOT NULL COMMENT '业务字段名称',
    `field_value` VARCHAR(128) COMMENT '业务字段值（支持文本、日期、数字，存储为字符串）',
    `field_type` VARCHAR(128) DEFAULT text COMMENT '字段类型（用于前端渲染）',
    `field_group` VARCHAR(50) COMMENT '字段分组（如：公文相关、合同相关、人事相关，用于前端分类展示）',
    `is_display` VARCHAR(128) DEFAULT yes COMMENT '是否在前端展示',
    `display_order` BIGINT DEFAULT 0 COMMENT '展示排序号（控制前端字段顺序）',
    `is_deleted` INT DEFAULT 0 COMMENT '删除标识（1为删除）',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT '「暂弃」档案动态业务字段附属表';


DROP TABLE IF EXISTS archive_process_record;
CREATE TABLE archive_process_record(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 办理记录 ID',
    `archive_id` BIGINT NOT NULL COMMENT ' 关联档案 ID',
    `process_type` VARCHAR(50) NOT NULL COMMENT ' 办理类型（拟稿 / 审核 / 签发 / 传阅 / 会签）',
    `process_user` BIGINT NOT NULL COMMENT '办理人',
    `process_time` DATETIME NOT NULL COMMENT ' 办理时间 ',
    `process_opinions` VARCHAR(128) COMMENT ' 办理意见（如：同意签发、需修改 XX 内容）',
    `process_status` VARCHAR(128) DEFAULT completed COMMENT ' 办理状态：待办理 / 已完成 / 已驳回 ',
    `next_user` BIGINT COMMENT '下一办理人 （流转下一步）',
    `process_attach_id` BIGINT COMMENT '办理附件 ID（如审核意见附件，关联 electronic_file 表）',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT '档案办理流程记录表';


DROP TABLE IF EXISTS borrow_application;
CREATE TABLE borrow_application(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 借阅申请 ID',
    `archive_id` BIGINT NOT NULL COMMENT ' 关联档案 ID',
    `applicant_id` BIGINT NOT NULL COMMENT ' 申请人 ID（系统用户）',
    `apply_time` DATETIME COMMENT ' 申请时间 ',
    `borrow_purpose` VARCHAR(2000) NOT NULL COMMENT ' 借阅用途（如：工作参考、审计检查）',
    `start_time` DATETIME NOT NULL COMMENT ' 预计借阅开始时间 ',
    `end_time` DATETIME NOT NULL COMMENT ' 预计归还时间 ',
    `approval_status` VARCHAR(64) DEFAULT pending COMMENT '审批状态：待审批 / 已通过 / 已驳回',
    `approver_username` BIGINT COMMENT '审批人',
    `approval_time` DATETIME COMMENT ' 审批时间 ',
    `approval_opinions` TEXT COMMENT '审批意见',
    `actual_return_time` DATETIME COMMENT '实际归还时间',
    `return_remarks` TEXT COMMENT '归还备注（如：文件完好、有污渍）',
    `status` VARCHAR(64) COMMENT '状态（未借用/借用中/已归还）',
    `remark` TEXT COMMENT '备注',
    `is_deleted` INT DEFAULT 0 COMMENT '删除标识（1为删除）',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT ' 档案借阅申请表 ';


DROP TABLE IF EXISTS api_call_log;
CREATE TABLE api_call_log(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 日志 ID',
    `system_id` BIGINT NOT NULL COMMENT ' 关联第三方系统 ID',
    `app_id` VARCHAR(100) NOT NULL COMMENT '调用时使用的 APPID（冗余，便于快速查询）',
    `client_ip` VARCHAR(50) COMMENT ' 第三方系统调用 IP 地址 ',
    `client_version` VARCHAR(50) COMMENT '第三方接口客户端版本',
    `api_path` VARCHAR(256) NOT NULL COMMENT ' 调用的档案系统接口路径（如：/api/v1/archive/upload）',
    `request_method` VARCHAR(256) COMMENT ' 请求方法（上传场景以 POST/PUT 为主）',
    `collection_id` BIGINT COMMENT 'archive_collection.collection_id',
    `auth_status` VARCHAR(64) NOT NULL COMMENT ' 身份认证状态 ',
    `auth_failed_reason` VARCHAR(256) COMMENT '认证失败原因（如：APPID 不存在、密钥错误）',
    `permission_check` VARCHAR(256) COMMENT '权限校验状态（认证通过后校验）',
    `related_archive_id` BIGINT COMMENT '上传成功的档案 ID（关联电子档案主表）',
    `related_file_ids` TEXT COMMENT '上传成功的文件 ID（多个用逗号分隔，关联电子文件表）',
    `upload_data_count` BIGINT COMMENT '本次上传数据条数（如：1 条档案 + 2 个附件）',
    `call_status` VARCHAR(64) NOT NULL COMMENT ' 调用结果：成功 / 失败 / 处理中 ',
    `error_code` VARCHAR(50) COMMENT ' 错误码（失败时，如：PARAM_ERROR、FILE_TOO_LARGE）',
    `error_msg` VARCHAR(128) COMMENT ' 错误详情（失败时记录）',
    `response_data` TEXT COMMENT ' 档案系统响应数据（成功时记录）',
    `call_time` DATETIME COMMENT ' 调用发起时间 ',
    `finish_time` DATETIME COMMENT ' 调用完成时间 ',
    `duration` BIGINT COMMENT ' 调用耗时（毫秒，finish_time - call_time）',
    PRIMARY KEY (`id`)
) COMMENT '第三方系统调用档案系统接口的日志表';


DROP TABLE IF EXISTS operation_log;
CREATE TABLE operation_log(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 日志 ID',
    `archive_id` BIGINT NOT NULL COMMENT ' 关联档案 ID',
    `operator_id` BIGINT NOT NULL COMMENT '操作人 ID（系统用户）',
    `operation_type` VARCHAR(64) NOT NULL COMMENT '操作类型',
    `operation_details` TEXT NOT NULL COMMENT '操作详情（如：修改字段：标题从 "XX 通知" 改为 "XX 重要通知"）',
    `operation_time` DATETIME COMMENT ' 操作时间 ',
    `ip_address` VARCHAR(50) COMMENT ' 操作 IP 地址 ',
    `client_type` VARCHAR(256) DEFAULT web COMMENT '操作客户端：网页 / APP / 接口',
    `remark` TEXT COMMENT '备注',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT '「暂弃」档案操作审计日志表';


DROP TABLE IF EXISTS archive_version;
CREATE TABLE archive_version(
    `id` BIGINT AUTO_INCREMENT COMMENT ' 版本 ID',
    `archive_id` BIGINT NOT NULL COMMENT ' 关联档案 ID',
    `version_no` VARCHAR(64) NOT NULL COMMENT ' 版本号（如：V1.0、V1.1）',
    `version_desc` TEXT COMMENT '版本说明（如：第一次修改标题、补充责任人信息）',
    `snapshot_data` TEXT NOT NULL COMMENT '版本快照（如：{"title":"旧标题","responsible_person":"旧责任人"}）',
    `remark` TEXT COMMENT '备注',
    `create_time` DATETIME COMMENT '创建时间',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT '「暂弃」档案版本控制表';


DROP TABLE IF EXISTS electronic_archive_seal;
CREATE TABLE electronic_archive_seal(
    `id` BIGINT AUTO_INCREMENT COMMENT '案系统唯一 ID',
    `archive_id` BIGINT COMMENT '整理库id',
    `fonds_no` VARCHAR(50) NOT NULL COMMENT ' 全宗号 ',
    `archive_no` VARCHAR(100) NOT NULL COMMENT '档号（唯一，如：全宗号 - 年度 - 目录号 - 序号）',
    `year` BIGINT NOT NULL COMMENT '年度（对应目录层级中的年度，如 2022）',
    `dept_id` BIGINT NOT NULL COMMENT '组织机构 ID（所属处室）',
    `dept_name` VARCHAR(256) NOT NULL COMMENT '组织机构名称（冗余）',
    `directory_id` BIGINT NOT NULL COMMENT '所属末级目录 ID',
    `file_title` VARCHAR(2048) COMMENT '案卷题名',
    `directory_no` VARCHAR(256) COMMENT '目录号',
    `archive_type` VARCHAR(256) COMMENT '档案类型',
    `file_no` VARCHAR(64) COMMENT '案卷号',
    `library_file_no` VARCHAR(256) COMMENT '馆编案卷号',
    `page_number` VARCHAR(64) COMMENT '张 (页) 号',
    `classification_no` VARCHAR(64) COMMENT '分类号',
    `serial_no` BIGINT NOT NULL COMMENT '序号（同目录同年度下的排序）',
    `doc_no` VARCHAR(100) COMMENT '文件编号',
    `title` VARCHAR(255) NOT NULL COMMENT '正题名（文件标题）',
    `responsible_person` VARCHAR(100) COMMENT '责任人（文件起草人 / 负责人）',
    `form_time` DATE NOT NULL COMMENT '形成时间（文件生成日期）',
    `retention_period` VARCHAR(64) NOT NULL COMMENT '保管期限：永久 / 30 年/ 10 年',
    `security_level` VARCHAR(64) NOT NULL COMMENT ' 密级：公开 / 秘密 / 机密 / 绝密 ',
    `page_count` BIGINT DEFAULT 0 COMMENT ' 文件页数 ',
    `database_level` VARCHAR(64) COMMENT '数据库级别',
    `organization_name` VARCHAR(256) COMMENT '所属机构',
    `draft_version` VARCHAR(64) COMMENT '稿本',
    `carrier_type` VARCHAR(64) COMMENT '载体类型',
    `carrier_specification` VARCHAR(256) COMMENT '载体规格',
    `carrier_quantity` BIGINT COMMENT '载体数量',
    `person_involved` VARCHAR(2048) COMMENT ' 文件所涉人名（相关人员）',
    `keywords` VARCHAR(2048) COMMENT ' 关键词（用于检索）',
    `subject_terms` VARCHAR(2048) COMMENT ' 主题词（规范检索用词）',
    `sort_order` BIGINT COMMENT ' 档案排序号（同目录下显示顺序）',
    `box_no` VARCHAR(50) COMMENT '盒号（对应实体档案盒编号，电子档案可冗余）',
    `note_text` TEXT COMMENT '备注全文（详细说明）',
    `fulltext_recognition` VARCHAR(64) COMMENT '全文识别状态：已识别 / 未识别 / 处理中',
    `note` TEXT COMMENT '附注（补充说明信息）',
    `archive_status` VARCHAR(50) COMMENT '整理状态：待整理/待归档/不归档/暂不归档',
    `status` VARCHAR(50) COMMENT '档案状态：有效 / 归档 / 销毁',
    `source_system_code` VARCHAR(50) COMMENT '来源系统编码（如第三方系统上传，关联 third_party_system.system_code）',
    `source_system_name` VARCHAR(50) COMMENT '来源系统名称（如第三方系统上传，关联 third_party_system.system_name）',
    `creator` VARCHAR(256) COMMENT '创建人姓名',
    `remarks` TEXT COMMENT '备注',
    `create_time` DATETIME COMMENT '创建时间',
    `sealer` VARCHAR(64) COMMENT '封存人',
    `sealer_username` VARCHAR(64) COMMENT '封存人用户名',
    `seal_time` DATETIME COMMENT '封存时间',
    `data_hash` VARCHAR(1024) COMMENT '防篡改 hash 值',
    `is_deleted` INT DEFAULT 0 COMMENT '删除标识（1为删除）',
    `create_by` VARCHAR(64) COMMENT '创建人',
    `update_time` DATETIME COMMENT '修改时间',
    `update_by` VARCHAR(64) COMMENT '修改人',
    `version` INT COMMENT '版本',
    PRIMARY KEY (`id`)
) COMMENT '档案数据备份库';

