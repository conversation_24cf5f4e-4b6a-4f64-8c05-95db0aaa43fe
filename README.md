
# 教育厅档案管理系统

一个基于 React + TypeScript + Vite 的现代化档案管理系统，使用 Radix UI 组件库和 Tailwind CSS v4 构建。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 📚 开发文档

完整的开发文档和规范请查看 [`docs/`](./docs/) 目录：

- **[开发文档总览](./docs/README.md)** - 快速开始和核心概念
- **[Augment 工作规则](./docs/development/AUGMENT_RULES.md)** - AI 开发规则和用户指南
- **[系统架构文档](./docs/architecture/SYSTEM_ARCHITECTURE.md)** - 技术架构和设计
- **[组件开发指南](./docs/guidelines/COMPONENT_GUIDELINES.md)** - 组件开发规范
- **[API 数据处理指南](./docs/guidelines/API_DATA_GUIDELINES.md)** - 数据层开发规范

## 🛠️ 技术栈

- **前端框架**: React 18.3.1 + TypeScript
- **构建工具**: Vite 6.3.5
- **UI 组件库**: Radix UI + shadcn/ui
- **样式系统**: Tailwind CSS v4
- **图标库**: Lucide React
- **图表库**: Recharts
- **状态管理**: React Hooks

## 📁 项目结构

```
src/
├── components/              # 组件目录
│   ├── ui/                 # 基础 UI 组件
│   ├── Layout.tsx          # 主布局组件
│   ├── Sidebar.tsx         # 侧边栏导航
│   └── [Module].tsx        # 业务模块组件
├── assets/                 # 静态资源
├── App.tsx                 # 根组件
├── main.tsx               # 应用入口
└── index.css              # 全局样式
```

## 🎯 核心功能模块

- **档案收集** - 电子档案采集和手动录入
- **档案整理** - 待归档库和归档库管理
- **综合检索** - 全文搜索和高级筛选
- **借阅管理** - 借阅申请和记录管理
- **数据分析** - 统计报表和可视化
- **系统管理** - 用户权限和参数配置

## 🔧 开发规范

### 组件开发
```typescript
interface ComponentProps {
  title: string;
  onAction?: (id: string) => void;
}

export function Component({ title, onAction }: ComponentProps) {
  const [state, setState] = useState<string>('');

  return (
    <div className="component-container">
      {/* 组件内容 */}
    </div>
  );
}
```

### 样式规范
```typescript
// 使用 Tailwind CSS 类名
<div className="flex items-center gap-4 p-6 bg-white rounded-lg shadow-md">

// 使用设计令牌
<div className="bg-primary text-primary-foreground">
```

## 📖 原始设计

This project is based on the Figma design: https://www.figma.com/design/RMQ5kG0F7rJLK1SZsQ5EUd/%E6%95%99%E8%82%B2%E5%8E%85%E6%A1%A3%E6%A1%88%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F--Copy-